# Hướng dẫn Deploy thay đổi Game G2

## 1. Backup Database
Trước khi deploy, hãy backup database:
```sql
mysqldump -u username -p database_name > backup_before_g2_changes.sql
```

## 2. Chạy Migration
Chạy migration để thêm column `opened_boxes`:
```sql
-- K<PERSON>t nối vào database và chạy:
ALTER TABLE `user_spins` 
ADD COLUMN `opened_boxes` TEXT NULL COMMENT 'JSON string storing array of box IDs that user has opened in game G2';
```

Hoặc chạy file migration:
```bash
mysql -u username -p database_name < src/migrations/add_opened_boxes_to_user_spins.sql
```

## 3. Deploy Code
1. Pull code mới từ repository
2. Build và restart application:
```bash
npm run build
pm2 restart app_name
```

## 4. Kiểm tra sau Deploy

### Test API endpoints:

#### 1. Test initGame cho G2:
```bash
curl -X GET "http://localhost:3001/api/game/g2/init" \
  -H "Authorization: Bearer YOUR_TOKEN"
```

Expected response có thêm:
```json
{
  "opened_boxes": [],
  "received_rewards": []
}
```

#### 2. Test claimVoucher cho G2:
```bash
curl -X POST "http://localhost:3001/api/game/g2/claim" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"box_ids": [1, 2, 3]}'
```

#### 3. Test shareForExtraTurn:
```bash
curl -X POST "http://localhost:3001/api/game/g2/share" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"platform": "facebook"}'
```

Expected response có thêm:
```json
{
  "is_share_turn": true
}
```

### Kiểm tra Database:
```sql
-- Kiểm tra column mới đã được thêm
DESCRIBE user_spins;

-- Kiểm tra dữ liệu totalSpinToday và openedBoxes
SELECT id, user_id, total_spin_today, opened_boxes 
FROM user_spins 
WHERE game_id = 28 
LIMIT 10;
```

## 5. Monitoring

### Các điểm cần monitor:
1. **API Response Time**: Đảm bảo performance không bị ảnh hưởng
2. **Database Performance**: Monitor query performance với column mới
3. **Error Logs**: Kiểm tra có lỗi nào liên quan đến JSON parsing không
4. **User Behavior**: Theo dõi totalSpinToday có được cập nhật đúng không

### Log files cần kiểm tra:
- Application logs
- Database slow query logs
- Error logs

## 6. Rollback Plan (nếu cần)

### Nếu có vấn đề nghiêm trọng:
1. Rollback code về version trước:
```bash
git checkout previous_commit_hash
npm run build
pm2 restart app_name
```

2. Rollback database (nếu cần):
```sql
-- Xóa column mới (chỉ nếu thực sự cần thiết)
ALTER TABLE `user_spins` DROP COLUMN `opened_boxes`;
```

3. Restore từ backup:
```bash
mysql -u username -p database_name < backup_before_g2_changes.sql
```

## 7. Post-Deploy Verification

### Checklist:
- [ ] Migration chạy thành công
- [ ] API initGame G2 trả về opened_boxes và received_rewards
- [ ] API claimVoucher G2 cập nhật totalSpinToday và openedBoxes
- [ ] API share cập nhật totalSpinToday và trả về is_share_turn
- [ ] Logic reset hàng ngày hoạt động đúng
- [ ] Performance không bị ảnh hưởng
- [ ] Không có error logs mới

### Test với real user data:
1. Tạo test user
2. Chơi game G2 hoàn chỉnh
3. Kiểm tra dữ liệu trong database
4. Test qua ngày mới để kiểm tra reset logic

## 8. Documentation Update
Cập nhật API documentation với các response mới:
- initGame response cho G2
- shareForExtraTurn response
- processNoTurns response cho G2
