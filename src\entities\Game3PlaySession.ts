import { Column, Entity, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'
import { TblUsers } from './TblUsers'
import { Location } from './Location'

export enum Game3SessionStatus {
  ACTIVE = 'active',
  COMPLETED = 'completed',
  EXPIRED = 'expired'
}

@Entity('game3_play_sessions', { schema: 'events' })
export class Game3PlaySession extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('varchar', { name: 'code', length: 255, unique: true })
  code: string

  @Column('int', { name: 'user_id' })
  userId: number

  @Column('int', { name: 'campaign_id' })
  campaignId: number

  @Column('int', { name: 'game_id' })
  gameId: number

  @Column('int', { name: 'location_id' })
  locationId: number

  @Column('enum', { 
    name: 'status', 
    enum: Game3SessionStatus,
    default: Game3SessionStatus.ACTIVE 
  })
  status: Game3SessionStatus

  @Column('json', { name: 'spawn_items', nullable: true })
  spawnItems: any[]

  @Column('json', { name: 'spawn_letters', nullable: true })
  spawnLetters: string[]

  @Column('datetime', { name: 'expires_at' })
  expiresAt: Date

  @ManyToOne(() => TblUsers)
  @JoinColumn({ name: 'user_id' })
  user: TblUsers

  @ManyToOne(() => Location)
  @JoinColumn({ name: 'location_id' })
  location: Location
}
