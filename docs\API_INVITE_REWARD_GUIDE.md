# API Hướng dẫn - Tặng quà cho người mời bạn bè

## Tổng quan

API này cho phép tặng phần thưởng cho người dùng đã mời bạn bè tham gia game. Chế độ hoạt động được cấu hình theo game:
- **add_spin**: Chỉ cộng lượt chơi (khi game.invitePrize !== 'give_gift')
- **give_gift**: Random tặng prize từ danh sách hợp lệ theo tỉ lệ trúng, bao gồm:
  - **voucher**: Voucher/coupon
  - **spin**: <PERSON><PERSON><PERSON><PERSON> chơi thêm
  - **lose**: Prize thua (khi hết voucher/spin)

## Endpoint

```
POST /api/game/{slug}/invite-reward
Authorization: Bearer {token}
Content-Type: application/json
```

## Request Body

```json
{
  "user_id": 123            // ID của người mời (bắt buộc)
}
```

### Tham số

#### URL Parameters
| Tham số | Loại | Bắt buộc | <PERSON>ô tả |
|---------|------|----------|--------|
| `slug` | string | Có | Game slug (g1, g2, v.v.) |

#### Request Body
| Tham số | Loại | Bắt buộc | Mô tả |
|---------|------|----------|--------|
| `user_id` | number | Có | ID của người mời (người được nhận phần thưởng) |

### Cấu hình game

Chế độ hoạt động được xác định bởi trường `invitePrize` trong bảng game:
- Nếu `game.invitePrize === 'give_gift'` → Chế độ tặng quà (random prize)
- Nếu khác → Chế độ cộng lượt chơi

### Người dùng request

- **Token Authorization**: Người được mời (người gọi API)
- **user_id trong body**: Người mời (người được nhận phần thưởng)

## Response

### Thành công - Chế độ cộng lượt chơi (Cả 2 người được cộng)

```json
{
  "status": 200,
  "error_code": 0,
  "message": "Cả 2 bạn đều được cộng thêm 1 lượt chơi nhờ kết bạn!",
  "data": {
    "inviter_user_id": 123,
    "invited_user_id": 456,
    "action": "add_spin",
    "inviter": {
      "play_turns": 4,
      "spin_counts": 3,
      "invite_count": 1
    },
    "invited": {
      "play_turns": 3,
      "spin_counts": 2,
      "invite_count": 1
    }
  }
}
```

### Đã được cộng lượt trước đó

```json
{
  "status": 200,
  "error_code": 0,
  "message": "Bạn đã được cộng lượt mời này rồi",
  "data": {
    "inviter_user_id": 123,
    "invited_user_id": 456,
    "already_rewarded": true,
    "is_reverse_invite": false
  }
}
```

### Cố gắng mời ngược lại

```json
{
  "status": 200,
  "error_code": 0,
  "message": "Bạn bè này đã mời bạn trước đó rồi. Không thể mời ngược lại!",
  "data": {
    "inviter_user_id": 123,
    "invited_user_id": 456,
    "already_rewarded": true,
    "is_reverse_invite": true
  }
}
```

### Thành công - Chế độ tặng quà (Cả 2 người được quà)

```json
{
  "status": 200,
  "error_code": 0,
  "message": "Cả 2 bạn đều nhận được quà tặng!",
  "data": {
    "inviter_user_id": 123,
    "invited_user_id": 456,
    "action": "give_gift",
    "inviter": {
      "prize": {
        "id": 8,
        "name": "Thêm 1 lượt chơi",
        "type": "spin",
        "voucher_code": "SPIN123",
        "voucher_name": "Lượt chơi miễn phí",
        "voucher_link": "https://example.com/qr-code",
        "voucher_expiry": "2024-12-31"
      },
      "message": "Bạn đã nhận được Thêm 1 lượt chơi!",
      "play_turns": 4,
      "spin_counts": 3,
      "invite_count": 1
    },
    "invited": {
      "prize": {
        "id": 12,
        "name": "Voucher 50k",
        "type": "voucher",
        "voucher_code": "VOUCHER456",
        "voucher_name": "Giảm giá 50k",
        "voucher_link": "https://example.com/voucher-qr",
        "voucher_expiry": "2024-12-31"
      },
      "message": "Bạn đã nhận được Voucher 50k!",
      "play_turns": 3,
      "spin_counts": 2,
      "invite_count": 1
    }
  }
}
```


### Lỗi

```json
{
  "status": 200,
  "error_code": 1,
  "message": "Game không tồn tại"
}
```

## Các trường hợp lỗi

| Error Code | Message | Mô tả |
|------------|---------|--------|
| 1 | Missing user_id (người mời) | Thiếu tham số user_id |
| 1 | Missing game slug | Thiếu slug trong URL |
| 1 | Invalid game slug | Slug không hợp lệ |
| 1 | Không thể mời chính mình | user_id trùng với ID người gọi API |
| 1 | Game không tồn tại | Game không tồn tại |
| 1 | User mời không tồn tại | user_id không tồn tại |
| 1 | Không có phần thưởng nào trong game | Không có prize nào active trong game |
| 1 | Không có phần thưởng hợp lệ | Hệ thống không thể chọn được prize hợp lệ |

## Logic xử lý

### Khi game.invitePrize !== "give_gift" (Chế độ cộng lượt)
1. **Cộng lượt cho cả 2 người**: Người mời và người được mời đều được cộng 1 vào `spinCounts` và `inviteCount` 
2. Tăng `inviteCount` lên 1
3. Không random prize, không lưu SpinHistory

### Khi game.invitePrize === "give_gift" (Chế độ tặng quà)
1. **Random prize cho cả 2 người**: Mỗi người được random 1 prize riêng biệt
2. Random từ tất cả active prizes (loại voucher, spin, lose)
3. Nếu voucher/spin còn số lượng, random theo `winrate`
4. Nếu hết voucher/spin, fallback sang lose prize
5. **Lưu 2 record vào `SpinHistory`**: Cho cả người mời và người được mời

### Xử lý coupon cho từng người
- **Prize type = "spin"**: Nếu có `bizStorageId`, gọi API Bizfly và lưu vào `VoucherDraw`
- **Prize type = "voucher"**: Nếu có `bizStorageId`, gọi API Bizfly để lấy coupon thật
- **Prize type = "lose"**: Không cộng lượt, không gọi API Bizfly

### Sau khi xử lý (cả 2 chế độ)
1. **Lưu vào `SpinHistory`**: 1 record cho mỗi người (chỉ với chế độ tặng quà)
2. **Tăng `inviteCount`**: Cả 2 người đều được cộng 1
3. Cập nhật thời gian `updatedAt` cho cả 2
4. **Lưu `InviteHistory`** để tracking và chống duplicate
5. Trả về thông tin cả 2 người với `play_turns` = `spinCounts + inviteCount`

### Cơ chế chống duplicate invite và mời ngược lại
1. **Kiểm tra cả 2 chiều**: Query `InviteHistory` với OR condition `(A→B OR B→A)`
2. **Nếu đã tồn tại cùng chiều**: Trả về "Bạn đã được cộng lượt mời này rồi"
3. **Nếu tồn tại chiều ngược**: Trả về "Bạn bè này đã mời bạn trước đó rồi. Không thể mời ngược lại!"
4. **Nếu chưa tồn tại**: Tiến hành tặng thưởng cho cả 2 và lưu vào `InviteHistory`

### Logic trừ lượt khi chơi game
1. Nếu `inviteCount > 0`: Trừ `inviteCount` trước
2. Nếu `inviteCount = 0`: Trừ `spinCounts`
3. `play_turns` luôn phản ánh tổng lượt còn lại

### Trường invite_count mới
- Được thêm vào entity `UserSpin`
- Đếm số lần người dùng được nhận phần thưởng từ việc mời bạn bè
- Tăng lên 1 mỗi khi gọi API thành công

## Ví dụ sử dụng

### JavaScript/Fetch
```javascript
const response = await fetch('/api/game/g1/invite-reward', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer your-token-here',
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    user_id: 123
  })
});

const result = await response.json();
console.log(result);
```

### cURL
```bash
curl -X POST "https://api.example.com/api/game/g1/invite-reward" \
  -H "Authorization: Bearer your-token-here" \
  -H "Content-Type: application/json" \
  -d '{
    "user_id": 123
  }'
```

## Ghi chú

1. **Bảo mật**: API yêu cầu token xác thực
2. **Logging**: Tất cả request đều được log vào `LogActivity`
3. **Tự động tạo UserSpin**: Nếu người mời chưa có record UserSpin, hệ thống sẽ tạo mới
4. **Số lượt mặc định theo game**: Sử dụng hàm `getDefaultSpinCountsByGameId()` để map theo gameId
5. **Chế độ auto-detect**: Dựa vào `game.invitePrize` để tự động xác định chế độ hoạt động
6. **Tính lượt chơi mới**: `play_turns = spinCounts + inviteCount` (tổng lượt có thể chơi)
7. **Logic trừ lượt**: Ưu tiên trừ `inviteCount` trước, sau đó mới trừ `spinCounts`
8. **Chống duplicate invite**: Mỗi cặp (inviter, invited) chỉ được cộng thưởng 1 lần per game/campaign
9. **Tracking invite history**: Sử dụng bảng `InviteHistory` để lưu trữ quan hệ mời và tránh duplicate
10. **Random theo tỉ lệ trúng**: Sử dụng hàm `randomPrizeWithQuantityCheck` với `winrate` của từng prize
11. **Fallback lose prize**: Khi hết voucher/spin, tự động random vào lose prize
12. **Spin prize đặc biệt**: Prize type = 'spin' sẽ lưu vào `VoucherDraw` nếu có `bizStorageId`
13. **Integration Bizfly**: Voucher/spin có `bizStorageId` sẽ được tích hợp với API Bizfly để lấy coupon thật 