import express from 'express'
import { gameG3API } from '@/api'
import { authenticate } from '@/middlewares/auth'

// Game G3 router
const gameG3Router = express.Router()

// GET /api/game/:slug/init
gameG3Router.get('/init', authenticate, gameG3API.initGameHandler)

// POST /api/game/rules
gameG3Router.post('/rules', authenticate, gameG3API.getRulesHandler)

// GET /api/game/locations
gameG3Router.get('/locations', authenticate, gameG3API.getLocationsHandler)

// GET /api/game/location-detail/:id
gameG3Router.get('/location-detail/:id', authenticate, gameG3API.getLocationDetailHandler)

// GET /api/game/play/:location_id (with slug support)
gameG3Router.get('/play/:location_id', authenticate, gameG3API.playGameHandler)

// POST /api/game/end
gameG3Router.post('/end', authenticate, gameG3API.endGameHandler)

// Export router
const router = express.Router()
router.use('/game3', gameG3Router)

export default router
