import { AppDataSource } from '@/config/config'
import nodemailer from 'nodemailer';
import bcrypt from 'bcryptjs'
import { TblUsers } from '@/entities'
import { MAIL_HOST, MAIL_POST, MAIL_SECURE, MAIL_USER, <PERSON><PERSON>_PASS, USER_NO_VERIFY, EMAIL_OTP_VERIFY_LINK, MAIL_TEMP, SMS_HOST, SMS_USER, SMS_PASS, SMS_BRAND_NAME, SMS_BRAND_CONTENT, USER_ONLINE_ROLE_ID, REG_MAIL_CONTENT, EMAIL_OTP_FORGOTPASS_LINK, FORGOTPASS_MAIL_CONTENT, GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, GOOGLE_CALLBACK, OTP_TIME_EXPIRE } from '@/config/constant'
import express from 'express'
import { jwtService } from '.'
import request from 'request';
import { UserStatus } from '@/entities/TblUsers'
import { google } from 'googleapis'
import axios from 'axios';
import { UpdateUserRequest } from '@/types/requests/user'
import { AuthorizedUserRequest } from '@/middlewares/auth'
import { NotFoundError, InputError } from '@/utils/ApiError'
import { commonService} from '@/services'

const userRepository = () => AppDataSource.getRepository(TblUsers);

function getRandomString(length) {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = '';

    for (let i = 0; i < length; i++) {
        const randomIndex = Math.floor(Math.random() * characters.length);
        result += characters[randomIndex];
    }

    return result;
}

export const getInfo = async (req: AuthorizedUserRequest) => {
    const { user } = req.authorizedUser;

    if(!(user)){
        return {
            status: 200,
            error_code: '',
            message: "not found"
        };
    }

    return {
        status: 200,
        code: '',
        data: {
            "id": user.id,
            "name": user.name,
            "phone": user.phone,
            "email": user.email,
            "address": user.address,
            "gender": user.gender == 1 ? "male" : (user.gender == 0 ? "female" : "other"),
            "birthday": user.birthday
        }
    };
}

export const userRegisFromAppEvent = async (req) => {
    try{
        const email = req.body.email;
        let userExist = false;
        let uid = parseInt(req.body.id);

        if(!(uid)){
            uid = 0;
        }

        if(!(email) && uid == 0){
            return {
                status: 200,
                code: 'EMAIL_ERR',
                error_code: 2,
                message: "email or id null"
            };
        }
    
        let user = null;
    
        if(uid){
            user = await userRepository().findOne({ where: { id: uid } });
        }
        else if(email){
            user = await userRepository().findOne({ where: { email: email } });
        }
    
        if(user == null){
            if(!(req.body.name)){
                return {
                    status: 200,
                    code: 'DATA_ERR',
                    error_code: 2,
                    message: "name empty"
                };
            }
    
            user = new TblUsers();
            user.external = 1;
        }
        else{
            userExist = true;
        }
    
        if(req.body.name && user.external == 1){
            user.name = req.body.name;
        }

        if(uid){
            if(req.body.email && user.external == 1){
                user.email = req.body.email;
            }
        }
    
        if(req.body.social && user.external == 1){
            user.social = req.body.social;
        }
    
        if(req.body.avatar && user.external == 1){
            user.avatar = req.body.avatar;
        }
    
        if(req.body.phone && user.external == 1){
            user.phone = req.body.phone;
        }
    
        if(req.body.device_id && user.external == 1){
            user.deviceId = req.body.device_id;
        }
    
        if(req.body.google_id && user.external == 1){
            user.googleId = req.body.google_id;
        }
    
        if(req.body.facebook_id && user.external == 1){
            user.facebookId = req.body.facebook_id;
        }
    
        if(userExist){        
            await userRepository().save(user);    
        }
        else{
            // Save the user to the database
            user.email = email;
            await userRepository().save(user);
        }
    
        return {
            status: 200,
            code: '',
            error_code: 0,
            data: {
                "id": user.id,
                "name": user.name,
                "social": user.social,
                "avatar": user.avatar,
                "email": user.email,
                "facebook_id": user.facebookId,
                "google_id": user.googleId,
                "apple_id": user.appleId
            }
        };
    }
    catch(ex){
        return {
            status: 200,
            code: 'DATA_ERR',
            error_code: 3,
            message: ex
        };
    }
}

export const updateUser = async (req: express.Request) => {
    try{
        if(!(req.params.id)){
            return {
                status: 200,
                code: 'ID_ERR',
                error_code: 2,
                message: "UserID null"
            };
        }
    
        const id = parseInt(req.params.id);

        if(!(id)){
            return {
                status: 200,
                code: 'ID_ERR',
                error_code: 2,
                message: "UserID null"
            };
        }

        let user = await userRepository().findOne({ where: { id: id } });
    
        if(user == null){
            return {
                status: 200,
                code: 'DATA_ERR',
                error_code: 3,
                message: "Data not found"
            };
        }
    
        if(req.body.name){
            user.name = req.body.name;
        }
    
        if(req.body.social){
            user.social = req.body.social;
        }
    
        if(req.body.avatar){
            user.avatar = req.body.avatar;
        }
    
        if(req.body.phone){
            user.phone = req.body.phone;
        }
    
        if(req.body.device_id){
            user.deviceId = req.body.device_id;
        }
    
        if(req.body.email){
            user.email = req.body.email;
        }

        if(req.body.city_id && parseInt(req.body.city_id)){
            user.cityId = parseInt(req.body.city_id);
        }

        if(req.body.district_id && parseInt(req.body.district_id)){
            user.districtId = parseInt(req.body.district_id);
        }        

        if(req.body.commune_id && parseInt(req.body.commune_id)){
            user.communeId = parseInt(req.body.commune_id);
        }

        if(req.body.address){
            user.address = req.body.address;
        }
    
        await userRepository().save(user);
    
        return {
            status: 200,
            error_code: 0,
            code: '',
            data: {
                "id": user.id,
                "name": user.name,
                "social": user.social,
                "avatar": user.avatar,
                "email": user.email,
                "facebook_id": user.facebookId,
                "google_id": user.googleId,
                "apple_id": user.appleId
            }
        };
    }
    catch(ex){
        return {
            status: 200,
            code: 'DATA_ERR',
            error_code: 3,
            message: ex
        };
    }
}

export const updateUserInfo = async (req: UpdateUserRequest) => {
    try{
        const { user } = req.authorizedUser;    
        const id = user.id;

        if(!(id)){
            return {
                status: 200,
                code: 'ID_ERR',
                error_code: 2,
                message: "UserID null"
            };
        }

        let userDb = await userRepository().findOne({ where: { id: id } });
    
        if(user == null){
            return {
                status: 200,
                code: 'DATA_ERR',
                error_code: 3,
                message: "Data not found"
            };
        }
    
        if(req.body.name){
            userDb.name = req.body.name;
        }
    
        if(req.body.social){
            userDb.social = req.body.social;
        }
    
        if(req.body.avatar){
            userDb.avatar = req.body.avatar;
        }
    
        if(req.body.device_id){
            userDb.deviceId = req.body.device_id;
        }
    
        if(req.body.email){
            const userCheck = await userRepository().findOne({ where: { email: req.body.email } });

            if(userCheck){
                if(userCheck.id != id)
                    return {
                        status: 200,
                        code: 'ID_ERR',
                        error_code: 2,
                        message: "email đã được đăng ký"
                    };
            }

            userDb.email = req.body.email;
        }
    
        if(req.body.phone){
            const userCheck = await userRepository().findOne({ where: { contactPhone: req.body.phone, contactPhoneAuthened: 1 } });

            if(userCheck){
                if(userCheck.id != id)
                    return {
                        status: 200,
                        code: 'ID_ERR',
                        error_code: 222,
                        message: "Số điện thoại đã được đăng ký"
                    };
            }

            if(!(userDb.contactPhone) || userDb.contactPhone != req.body.phone){
                userDb.contactPhone = req.body.phone;

                if(userDb.phone && userDb.phone == req.body.phone){
                    userDb.contactPhoneAuthened = 1;
                }
                else{
                    userDb.contactPhoneAuthened = 0;
                }
            }
        }

        if(req.body.city_id && parseInt(req.body.city_id)){
            userDb.cityId = parseInt(req.body.city_id);
        }

        if(req.body.district_id && parseInt(req.body.district_id)){
            userDb.districtId = parseInt(req.body.district_id);
        }        

        if(req.body.commune_id && parseInt(req.body.commune_id)){
            userDb.communeId = parseInt(req.body.commune_id);
        }

        if(req.body.address){
            userDb.address = req.body.address;
        }

        if(req.body.is_update_info != null && parseInt(req.body.is_update_info)){
            userDb.isUpdateInfo = parseInt(req.body.is_update_info);
        }
            
        await userRepository().save(userDb);
    
        return {
            status: 200,
            error_code: 0,
            code: '',
            data: {
                "id": userDb.id,
                "name": userDb.name,
                "social": userDb.social,
                "avatar": userDb.avatar,
                "email": userDb.email,
                "facebook_id": userDb.facebookId,
                "google_id": userDb.googleId,
                "apple_id": userDb.appleId,
                "phone": userDb.phone,
                contact_phone : userDb.contactPhone,
                contact_phone_authened: userDb.contactPhoneAuthened
            }
        };
    }
    catch(ex){
        return {
            status: 200,
            code: 'DATA_ERR',
            error_code: 3,
            message: ex
        };
    }
}
  
export const regUserByEmail = async (email, password, req: express.Request) => {
    return new Promise(async (resolve) =>{
        const USER_EXTERNAL = 3;

        try{
            if(email.indexOf(MAIL_TEMP) >= 0){
                return resolve({
                    status: 200,
                    error_code: 1,
                    message: "Email không hợp lệ"
                });
            }

            let user = await userRepository().findOne({ where: { email: email } });

            if(user){
                if(user.active == USER_NO_VERIFY && user.external == USER_EXTERNAL && user.indentifyCode){
                    //continue;
                }
                else{
                    return resolve({
                        status: 200,
                        error_code: 6,
                        message: "Email này đã được đăng ký"
                    });
                }
            }

            const transporter = nodemailer.createTransport({
                host: MAIL_HOST, // Thay bằng máy chủ SMTP của bạn
                port: MAIL_POST, // Cổng SMTP (thường là 465 cho SSL hoặc 587 cho TLS)
                secure: MAIL_SECURE, // true cho SSL, false cho các giao thức khác
                auth: {
                    user: MAIL_USER, // Thay bằng email của bạn
                    pass: MAIL_PASS   // Thay bằng mật khẩu email của bạn
                }
            });

            const r = getRandomString(75);
            
            // Tạo email chứa mã OTP
            let verifyLink = EMAIL_OTP_VERIFY_LINK.replace('[EMAIL]', email);
            verifyLink = verifyLink.replace('[KEY]', r);

            const mailContent = REG_MAIL_CONTENT.replace('[LINK_VERIFY_EMAIL]', verifyLink);

            const mailOptions = {
            from: {
                name: 'KAM gift',
                address: MAIL_USER
            },
            to: email,
            subject: `KAM verify account`,
            html: mailContent
            };
    
            transporter.sendMail(mailOptions, async (error, info) => {
                if (error) {
                    return resolve({
                    status: 200,
                    error_code: 1,
                    message: error
                    });
                }

                if(user == null){      
                    user = new TblUsers();
                    user.email = email;
                    user.name = email;
                    
                    if(req.body.name) user.name = req.body.name;
                    if(req.body.phone) user.phone = req.body.phone;

                    user.active = USER_NO_VERIFY;
                    user.roleId = USER_ONLINE_ROLE_ID;
                    user.external = USER_EXTERNAL;
                }

                if(password){
                    const hashedPassword = await bcrypt.hash(password, 8);                
                    user.password = hashedPassword;
                }

                user.remmemberToken = r;
                user.otpSentAt = Math.round(+new Date() / 1000);

                await userRepository().save(user);

                return resolve({
                    status: 200,
                    error_code: 0,
                    data: {
                        "email": user.email,
                        //"otp": user.indentifyCode,
                        //"key": user.remmemberToken
                    }
                });
            });  
        }
        catch(ex){
            return resolve({
            status: 200,
            error_code: 1,
            message: ex
            });
        }
    });    
}

export const verifyOtp = async (email, phone, otp) =>{
    return new Promise(async (resolve) =>{
        try{
            let user = null;

            if(email){
                user = await userRepository().findOne({ where: { email: email } });

                if(user == null){ 
                    return resolve({
                        status: 200,
                        error_code: 1,
                        message: "email không tồn tại"
                    });
                }
            }
            else if(phone){
                user = await userRepository().findOne({ where: { phone: phone } });

                if(user == null){ 
                    return resolve({
                        status: 200,
                        error_code: 1,
                        message: "số điện thoại không tồn tại"
                    });
                }
            }

            if(user == null){ 
                return resolve({
                    status: 200,
                    error_code: 1,
                    message: "user không tồn tại"
                });
            }

            const currTime = Math.round(+new Date() / 1000);

            if(user.otpSentAt){
                if(currTime - OTP_TIME_EXPIRE >= user.otpSentAt){
                    return resolve({
                        status: 200,
                        error_code: 10,
                        message: "OTP is expired",
                        data: null
                    });
                }
            }

            if(otp && (user.indentifyCode != otp)){
                return resolve({
                    status: 200,
                    error_code: 2,
                    message: "OTP không chính xác"
                });
            }
            
            return resolve({
                status: 200,
                error_code: 0,
                message: "OTP hợp lệ",
                data: {
                  }
            });
        }
        catch(ex){
            return resolve({
                status: 200,
                error_code: 4,
                message: ex
            });
        }
    });
}

export const verifyOtpViaEmail = async (email, otp, key) =>{
    return new Promise(async (resolve) =>{
        try{
            const user = await userRepository().findOne({ where: { email: email } });

            if(user == null){ 
                return resolve({
                    status: 200,
                    error_code: 1,
                    message: "email không tồn tại"
                });
            }

            if(key){
                const currTime = Math.round(+new Date() / 1000);

                if(user.otpSentAt){
                    if(currTime - OTP_TIME_EXPIRE >= user.otpSentAt){
                        return resolve({
                            status: 200,
                            error_code: 10,
                            message: "OTP is expired",
                            data: null
                        });
                    }
                }
            }

            if(otp && (user.indentifyCode != otp)){
                return resolve({
                    status: 200,
                    error_code: 2,
                    message: "OTP không chính xác"
                });
            }

            if(user.remmemberToken != key){
                return resolve({
                    status: 200,
                    error_code: 3,
                    message: "Token không chính xác"
                });
            }

            user.remmemberToken = "";
            user.indentifyCode = "";
            user.active = 1;
            const ACCESS_TOKEN = jwtService.generateAccessToken(user.id, user.roleId);
            const REFRESH_TOKEN = jwtService.generateRefreshToken(user.id, user.roleId)

            user.isLoging = 1;
            user.accessToken = ACCESS_TOKEN;
            user.refreshToken = REFRESH_TOKEN;
            user.otpSentAt = 0;

            await userRepository().save(user);
            
            return resolve({
                status: 200,
                error_code: 0,
                message: "Xác thực thành công. TK đã được active",
                data: {
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    phone: user.phone,
                    roleId: user.roleId,
                    active: user.active,
                    avatar: user.avatar,
                    accessToken: user.accessToken,
                    is_update_info: user.isUpdateInfo,                    
                    contact_phone : user.contactPhone,
                    contact_phone_authened: user.contactPhoneAuthened
                  }
            });
        }
        catch(ex){
            return resolve({
                status: 200,
                error_code: 4,
                message: ex
            });
        }
    });
}

export const recoverPasswordByEmail = async (email, otp) => {
    return new Promise(async (resolve) =>{
      try{
        if(email.indexOf(MAIL_TEMP) >= 0){
            return resolve({
                status: 200,
                error_code: 1,
                message: "Email không hợp lệ"
            });
        }

        let user = await userRepository().findOne({ where: { email: email } });

        if(!user){
            return resolve({
                status: 200,
                error_code: 1,
                message: "Email không tồn tại"
            });
        }

        if (user.active !== UserStatus.ACTIVE) {
            // user account is not active
            //throw new ApiError(200, 'E102', 'User account is not active')
            return resolve({
                status: 200,
                error_code: 1,
                message: "User account is not active"
            });
        }

        const transporter = nodemailer.createTransport({
            host: MAIL_HOST, // Thay bằng máy chủ SMTP của bạn
            port: MAIL_POST, // Cổng SMTP (thường là 465 cho SSL hoặc 587 cho TLS)
            secure: MAIL_SECURE, // true cho SSL, false cho các giao thức khác
            auth: {
              user: MAIL_USER, // Thay bằng email của bạn
              pass: MAIL_PASS   // Thay bằng mật khẩu email của bạn
            }
        });

        
        // Tạo email chứa mã OTP
        let verifyLink = EMAIL_OTP_FORGOTPASS_LINK.replace('[EMAIL]', email);
        verifyLink = verifyLink.replace('[KEY]', otp);

        const mailContent = FORGOTPASS_MAIL_CONTENT.replace('[LINK_VERIFY_EMAIL]', verifyLink);

        const mailOptions = {
          from: {
            name: 'Kamgift',
            address: MAIL_USER
          },
          to: email,
          subject: `Lấy lại mật khẩu`,
          html: mailContent
        };
  
        transporter.sendMail(mailOptions, async (error, info) => {
            if (error) {
                return resolve({
                  status: 200,
                  error_code: 1,
                  message: error
                });
            }

            user.indentifyCode = otp;
            user.otpSentAt = Math.round(+new Date() / 1000);

            await userRepository().save(user);

            return resolve({
                status: 200,
                error_code: 0,
                data: {
                    "email": user.email,
                    //"otp": user.indentifyCode
                }
            });
        });  
      }
      catch(ex){
        return resolve({
          status: 200,
          error_code: 1,
          message: ex
        });
      }
    });    
}

export const resetPasswordByEmail = async (email, otp, password) => {
    return new Promise(async (resolve) =>{
      try{
        if(!(password)){
            return resolve({
                status: 200,
                error_code: 6,
                message: "Mật khẩu không hợp lệ"
            });
        }

        const user = await userRepository().findOne({ where: { email: email } });
        const currTime = Math.round(+new Date() / 1000);

        if(!user){
            return resolve({
                status: 200,
                error_code: 7,
                message: "Email không tồn tại"
            });
        }

        if(!otp){
            return resolve({
                status: 200,
                error_code: 7,
                message: "Chưa nhập OTP"
            });
        }

        if(user.otpSentAt){
            if(currTime - OTP_TIME_EXPIRE >= user.otpSentAt){
                return resolve({
                    status: 200,
                    error_code: 10,
                    message: "OTP hết hạn",
                    data: null
                });
            }
        }

        if(user.indentifyCode == otp){
            const hashedPassword = await bcrypt.hash(password, 8);

            user.password = hashedPassword;

            if(otp){
                user.indentifyCode = '';
                user.otpSentAt = 0;
            }
            
            await userRepository().save(user);
            
            return resolve({
                status: 200,
                error_code: 0,
                message: "Reset mật khẩu thành công",
                data: {
                    "email": user.email
                }
            });
        }
        else{
            return resolve({
                status: 200,
                error_code: 1,
                message: "OTP không chính xác",
                data: {
                    "email": user.email
                }
            });
        }
      }
      catch(ex){
        return resolve({
          status: 200,
          error_code: 1,
          message: ex
        });
      }
    });    
}

export const changePasswordByEmail = async (email, passwordOld, passwordNew) => {
    return new Promise(async (resolve) =>{
      try{
        if(email == null || email == "" || email.indexOf(MAIL_TEMP) >= 0){
            return resolve({
                status: 200,
                error_code: 1,
                message: "Email không hợp lệ"
            });
        }

        if(!(passwordNew)){
            return resolve({
                status: 200,
                error_code: 6,
                message: "Mật khẩu không hợp lệ"
            });
        }

        const user = await userRepository().findOne({ where: { email: email } });

        if(!user){
            return resolve({
                status: 200,
                error_code: 7,
                message: "Email không tồn tại"
            });
        }

        let isMatch = 0;

        if(passwordOld){
            const hashedPass = user.password;
            const isMatchPass = await bcrypt.compare(passwordOld, hashedPass);

            if(isMatchPass){
                isMatch = 2;
            }
            else{
                isMatch = -2;
            }
        }
        else{
            return resolve({
                status: 200,
                error_code: 6,
                message: "Chưa nhập mật khẩu cũ"
            });
        }

        if(isMatch > 0){
            const hashedPassword = await bcrypt.hash(passwordNew, 8);

            user.password = hashedPassword;
            
            await userRepository().save(user);
            
            return resolve({
                status: 200,
                error_code: 0,
                message: "Đổi mật khẩu thành công",
                data: {
                    "email": user.email
                }
            });
        }

        if(isMatch == -1){            
            return resolve({
                status: 200,
                error_code: 1,
                message: "OTP không chính xác",
                data: {
                    "email": user.email
                }
            });
        }

        if(isMatch == -2){            
            return resolve({
                status: 200,
                error_code: 2,
                message: "Mật khẩu cũ không chính xác",
                data: {
                    "email": user.email
                }
            });
        }

        return resolve({
            status: 200,
            error_code: 3,
            message: "Lỗi không xác định",
            data: {
                "email": user.email
            }
        });
      }
      catch(ex){
        return resolve({
          status: 200,
          error_code: 1,
          message: ex
        });
      }
    });    
}


export const resetPasswordByPhone = async (phone, otp, password) => {
    return new Promise(async (resolve) =>{
      try{
        if(!(password)){
            return resolve({
                status: 200,
                error_code: 6,
                message: "Mật khẩu không hợp lệ"
            });
        }

        const user = await userRepository().findOne({ where: { phone: phone } });
        const currTime = Math.round(+new Date() / 1000);

        if(!user){
            return resolve({
                status: 200,
                error_code: 7,
                message: "Số điện thoại không tồn tại"
            });
        }

        if(!otp){
            return resolve({
                status: 200,
                error_code: 7,
                message: "Chưa nhập OTP"
            });
        }

        if(user.otpSentAt){
            if(currTime - OTP_TIME_EXPIRE >= user.otpSentAt){
                return resolve({
                    status: 200,
                    error_code: 10,
                    message: "OTP hết hạn",
                    data: null
                });
            }
        }

        if(user.indentifyCode == otp){
            const hashedPassword = await bcrypt.hash(password, 8);

            user.password = hashedPassword;

            if(otp){
                user.indentifyCode = '';
                user.otpSentAt = 0;
            }
            
            await userRepository().save(user);
            
            return resolve({
                status: 200,
                error_code: 0,
                message: "Reset mật khẩu thành công",
                data: {
                    "phoe": user.phone
                }
            });
        }
        else{
            return resolve({
                status: 200,
                error_code: 1,
                message: "OTP không chính xác",
                data: {
                    "phoe": user.phone
                }
            });
        }
      }
      catch(ex){
        return resolve({
          status: 200,
          error_code: 1,
          message: ex
        });
      }
    });    
}

export const verifyOtpViaPhone = async (number, otp) =>{
    return new Promise(async (resolve) =>{
        try{
            if(!number || !otp){ 
                return resolve({
                    status: 200,
                    error_code: 1,
                    message: "Chưa nhập số điện thoại hoặc otp"
                });
            }

            const user = await userRepository().findOne({ where: { phone: number } });

            if(user == null){ 
                return resolve({
                    status: 200,
                    error_code: 1,
                    message: "Số điện thoại không tồn tại"
                });
            }

            if(otp){
                const currTime = Math.round(+new Date() / 1000);

                if(user.otpSentAt){
                    if(currTime - OTP_TIME_EXPIRE >= user.otpSentAt){
                        return resolve({
                            status: 200,
                            error_code: 10,
                            message: "OTP is expired",
                            data: null
                        });
                    }
                }
            }

            if(user.indentifyCode != otp){
                return resolve({
                    status: 200,
                    error_code: 2,
                    message: "OTP không chính xác"
                });
            }

            user.remmemberToken = "";
            user.indentifyCode = "";
            user.active = 1;

            // const ACCESS_TOKEN = jwtService.generateAccessToken(user.id, user.roleId);
            // const REFRESH_TOKEN = jwtService.generateRefreshToken(user.id, user.roleId)

            // user.isLoging = 1;
            // user.accessToken = ACCESS_TOKEN;
            // user.refreshToken = REFRESH_TOKEN;

            if(!user.contactPhone){
                const phone = number;
                const contact_phone = number;
                const id = user.id;

                const usersCheck = await userRepository()
                    .createQueryBuilder('users')
                    .where('(phone = :phone OR contact_phone = :contact_phone)', {
                        phone,
                        contact_phone,
                    })
                    .andWhere('id != :id', { id })
                    .getMany();

                if(usersCheck == null || (usersCheck != null && usersCheck.length == 0)){
                    user.contactPhone = number;
                    user.contactPhoneAuthened = 1;
                }                
            }

            user.otpSentAt = 0;

            await userRepository().save(user);
            
            return resolve({
                status: 200,
                error_code: 0,
                message: "Xác thực thành công. Tài khoản đã được active",
                data: {
                    id: user.id,
                    name: user.name,
                    email: user.email,
                    phone: user.phone,
                    roleId: user.roleId,
                    active: user.active,
                    avatar: user.avatar,
                    is_update_info: user.isUpdateInfo,
                    contact_phone: user.contactPhone,
                    contact_phone_authened: user.contactPhoneAuthened
                    //accessToken: user.accessToken
                  }
            });            
        }
        catch(ex){
            return resolve({
                status: 200,
                error_code: 3,
                message: ex
            });
        }
    });
}

export const changePasswordByPhone = async (phone, passwordOld, passwordNew) => {
    return new Promise(async (resolve) =>{
      try{
        if(phone == null || phone == ""){
            return resolve({
                status: 200,
                error_code: 1,
                message: "Số điện thoại không hợp lệ"
            });
        }

        if(!(passwordNew)){
            return resolve({
                status: 200,
                error_code: 6,
                message: "Mật khẩu không hợp lệ"
            });
        }

        const user = await userRepository().findOne({ where: { phone: phone } });

        if(!user){
            return resolve({
                status: 200,
                error_code: 7,
                message: "Số điện thoại không tồn tại"
            });
        }

        let isMatch = 0;

        if(passwordOld){
            const hashedPass = user.password;
            const isMatchPass = await bcrypt.compare(passwordOld, hashedPass);

            if(isMatchPass){
                isMatch = 2;
            }
            else{
                isMatch = -2;
            }
        }

        if(isMatch > 0){
            const hashedPassword = await bcrypt.hash(passwordNew, 8);

            user.password = hashedPassword;            
            
            await userRepository().save(user);
            
            return resolve({
                status: 200,
                error_code: 0,
                message: "Đổi mật khẩu thành công",
                data: {
                    "phone": user.phone
                }
            });
        }

        if(isMatch == -1){            
            return resolve({
                status: 200,
                error_code: 1,
                message: "OTP không chính xác",
                data: {
                    "phone": user.phone
                }
            });
        }

        if(isMatch == -2){            
            return resolve({
                status: 200,
                error_code: 2,
                message: "Mật khẩu cũ không chính xác",
                data: {
                    "phone": user.phone
                }
            });
        }

        return resolve({
            status: 200,
            error_code: 3,
            message: "Lỗi không xác định",
            data: {
                "phone": user.phone
            }
        });
      }
      catch(ex){
        return resolve({
          status: 200,
          error_code: 1,
          message: ex
        });
      }
    });    
}

export const reqVerifyPhoneContact = async (req: AuthorizedUserRequest) =>{
    return new Promise(async (resolve) =>{
        try{
            const { user } = req.authorizedUser;
            const {phone} = req.body;
            const userid = user.id;
            const id = userid;
            const otp = Math.floor(100000 + Math.random() * 900000).toString();
            const userDb = await userRepository().findOne({ where: { id: userid } });
            const contact_phone = phone;

            if(!phone){
                return resolve({
                    status: 200,
                    error_code: 1,
                    message: "Số điện thoại không hợp lệ"
                });
            }

            const usersCheck = await userRepository()
                .createQueryBuilder('users')
                .where('((contact_phone = :contact_phone and contact_phone_authened = 1))', {
                    contact_phone,
                })
                .andWhere('id != :id', { id })
                .getMany();
            
            if(usersCheck && usersCheck.length > 0){
                return resolve({
                    status: 200,
                    error_code: 2,
                    message: "Số điện thoại đã được sử dụng"
                });
            }        
            
            if(!userDb){
                return resolve({
                    status: 200,
                    error_code: 5,
                    message: "User không tồn tại"
                });
            }

            if(userDb.contactPhone == phone && userDb.contactPhoneAuthened == 1){
                return resolve({
                    status: 200,
                    error_code: 0,
                    message: "Số điện thoại liên hệ đã được xác thực",
                    data: {
                        phone_status: 1
                    }
                });
            }

            const options = {
                'method': 'POST',
                'url': SMS_HOST,
                'headers': {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    "username": SMS_USER,
                    "password": SMS_PASS,
                    "message": SMS_BRAND_CONTENT.replace('[OTP]', otp),
                    "brandname": SMS_BRAND_NAME,
                    "recipients": [
                        {
                            "message_id": new Date().getTime().toString(),
                            "number": phone
                        }
                    ]
                })
            };

            request(options, async function (error, response) {
                if (error) {
                    return resolve({
                        status: 200,
                        error_code: 4,
                        message: error
                    });
                }

                const dtm = JSON.parse(response.body);
                let dataResult = null;

                if(dtm){
                    if(Array.isArray(dtm)){
                        if(dtm.length > 0){
                            dataResult = dtm[0];
                        }                        
                    }else{
                        dataResult = dtm;
                    }
                }

                if(dataResult){
                    if(dataResult.status == "DELIVERED" || dataResult.status == "SENT"){
                        userDb.contactPhone = phone;
                        userDb.otpContactSentAt = Math.round(+new Date() / 1000);
                        userDb.contactPhoneOtp = otp;
                        userDb.contactPhoneAuthened = 0;
            
                        await userRepository().save(userDb);
        
                        return resolve({
                            status: 200,
                            error_code: 0,
                            data: response.body
                        });
                    }
                    else{
                        return resolve({
                            status: 200,
                            error_code: 5,
                            message: response.body
                        });
                    }
                    
                }
                else{
                    return resolve({
                        status: 200,
                        error_code: 6,
                        message: response.body
                    });
                }                
            });
        }
        catch(ex){
            resolve({
                status: 200,
                error_code: 3,
                message: ex
            });
        }
    });    
}

export const verifyOtpViaPhoneContact = async (phoneNumber, otp, req: AuthorizedUserRequest) =>{
    return new Promise(async (resolve) =>{
        try{            
            const { user } = req.authorizedUser;
            const id = user.id;
            const contact_phone = phoneNumber;

            if(!phoneNumber || !otp){ 
                return resolve({
                    status: 200,
                    error_code: 1,
                    message: "Chưa nhập số điện thoại hoặc otp"
                });
            }

            const userDB = await userRepository().findOne({ where: { id: id, contactPhone: phoneNumber } });

            if(userDB == null){ 
                return resolve({
                    status: 200,
                    error_code: 1,
                    message: "Số điện thoại không tồn tại"
                });
            }

            const usersCheck = await userRepository()
                .createQueryBuilder('users')
                .where('((contact_phone = :contact_phone and contact_phone_authened = 1))', {
                    contact_phone,
                })
                .andWhere('id != :id', { id })
                .getMany();
            
            if(usersCheck && usersCheck.length > 0){
                return resolve({
                    status: 200,
                    error_code: 2,
                    message: "Số điện thoại đã được sử dụng"
                });
            }

            if(otp){
                const currTime = Math.round(+new Date() / 1000);

                if(userDB.otpContactSentAt){
                    if(currTime - OTP_TIME_EXPIRE >= userDB.otpContactSentAt){
                        return resolve({
                            status: 200,
                            error_code: 10,
                            message: "OTP is expired",
                            data: null
                        });
                    }
                }
            }

            if(userDB.contactPhoneOtp != otp){
                return resolve({
                    status: 200,
                    error_code: 2,
                    message: "OTP không chính xác"
                });
            }

            userDB.otpContactSentAt = 0;
            userDB.contactPhoneOtp = '';
            userDB.contactPhoneAuthened = 1;

            await userRepository().save(userDB);
            
            return resolve({
                status: 200,
                error_code: 0,
                message: "Xác thực số điện thoại liên hệ thành công",
                data: {
                }
            });            
        }
        catch(ex){
            return resolve({
                status: 200,
                error_code: 3,
                message: ex
            });
        }
    });
}

export const reloadZaloAuthen = async (req: AuthorizedUserRequest, isReset = 0) =>{
    return new Promise(async (resolve) =>{
        try{            
            const { user } = req.authorizedUser;
            const userid = user.id;

            const userDB = await userRepository().findOne({ where: { id: userid } });

            if(userDB == null){ 
                return resolve({
                    status: 200,
                    error_code: 1,
                    message: "Số điện thoại không tồn tại"
                });
            }

            if(userDB.email != '<EMAIL>'){ 
                return resolve({
                    status: 200,
                    error_code: 10,
                    message: "TK này không được phép dùng API này"
                });
            }

            if(isReset) global.ZaloAuthenInfo.isReloadAuthenInfo = 1;

            return resolve({
                status: 200,
                error_code: 0,
                message: "Reset Zalo config thành công!",
                data: {
                    isReloadAuthenInfo: global.ZaloAuthenInfo.isReloadAuthenInfo,
                    "refreshToken": global.ZaloAuthenInfo.refreshToken,
                    "accessToken": global.ZaloAuthenInfo.accessToken,
                    "timeRefreshToken": global.ZaloAuthenInfo.timeRefreshToken
                }
            });            
        }
        catch(ex){
            return resolve({
                status: 200,
                error_code: 3,
                message: ex
            });
        }
    });
}

//google
function createOAuth2Client(
    clientId: string,
    clientSecret: string,
    redirectUri: string,
    accessToken: string
) {
    const oauth2Client = new google.auth.OAuth2(clientId, clientSecret, redirectUri);

    oauth2Client.setCredentials({
        access_token: accessToken, // Set the access token
    });

    return oauth2Client;
}

async function getGoogleUserInfo(ACCESS_TOKEN) {
    try {
        const oauth2Client = createOAuth2Client(GOOGLE_CLIENT_ID, GOOGLE_CLIENT_SECRET, GOOGLE_CALLBACK, ACCESS_TOKEN);
        const oauth2 = google.oauth2({ version: 'v2', auth: oauth2Client });
        const response = await oauth2.userinfo.get();

        //console.log('User Info:', response.data);

        return {
            error_code: 0,
            data: response.data
        };
    } 
    catch (error) {
        //console.error('Error fetching user info:', error);
        //throw error;

        return {
            error_code: 1,
            data: error
        };
    }
}

function sleep(seconds: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, seconds * 1000));
}

export const regUserViaGoogle = async (access_token, email, google_id) =>{
    return new Promise(async (resolve) =>{
        try{
            let user = await userRepository().findOne({ where: { email: email } });
            const googleUser = await getGoogleUserInfo(access_token);

            if(googleUser.error_code == 0){
                if(googleUser.data.email != email){
                    return resolve({
                        status: 200,
                        error_code: 4,
                        message: "email không chính xác",
                        data: googleUser.data
                    });
                }

                if( (googleUser.data.sub && googleUser.data.sub == google_id) || (googleUser.data.id && googleUser.data.id == google_id)){
                    console.log(`google authen success`);
                }else{
                    return resolve({
                        status: 200,
                        error_code: 4,
                        message: "google_id hoặc access_token không chính xác",
                        data: googleUser.data
                    });
                }

                if(user == null || user == undefined){
                    user = new TblUsers();
                    user.email = googleUser.data.email;
                    user.name = googleUser.data.name;
                    user.avatar = googleUser.data.picture;
                    user.googleId = googleUser.data.id;
                    user.roleId = 12;
                    user.external = 2;

                    await userRepository().save(user);

                    await sleep(1);
                }

                const ACCESS_TOKEN = jwtService.generateAccessToken(user.id, user.roleId);
                const REFRESH_TOKEN = jwtService.generateRefreshToken(user.id, user.roleId)

                user.isLoging = 1;
                user.accessToken = ACCESS_TOKEN;
                user.refreshToken = REFRESH_TOKEN;

                await userRepository().save(user);
                
                return resolve({
                    status: 200,
                    error_code: 0,
                    message: "Xác thực thành công. TK đã được active",
                    data: {
                        id: user.id,
                        name: user.name,
                        email: user.email,
                        phone: user.phone,
                        roleId: user.roleId,
                        active: user.active,
                        avatar: user.avatar,
                        accessToken: user.accessToken,
                        is_update_info: user.isUpdateInfo,                        
                        contact_phone : user.contactPhone,
                        contact_phone_authened: user.contactPhoneAuthened
                      }
                });
                
            }
            else{
                return resolve({
                    status: 200,
                    error_code: 5,
                    message: "email và access_token không khớp",
                    data: googleUser.data
                });
            }
        }
        catch(ex){
            resolve({
                status: 200,
                error_code: 3,
                message: ex
            });
        }
    });
}

async function getFacebookUserInfo(accessToken: string) {
    const url = `https://graph.facebook.com/v12.0/me`;
    const fields = 'id,name,email'; // Fields to fetch
  
    try {
      const response = await axios.get(url, {
        params: {
          fields, // Specify the fields to retrieve
          access_token: accessToken, // Provide the access token
        },
      });
  
      // Return the data from the API
      return {
        error_code: 0,
        data: response.data
      };
    } catch (error) {
      console.error('Error fetching user info from Facebook:', error.response?.data || error.message);

      return {
        error_code: 1,
        data: error
      };
      //throw error; // Re-throw the error for further handling
    }
  }

export const regUserViaFacebook = async (access_token, email, facebook_id) =>{
    return new Promise(async (resolve) =>{
        try{
            let user = await userRepository().findOne({ where: { facebookId: facebook_id } });
            const fbleUser = await getFacebookUserInfo(access_token);

            if(fbleUser.error_code == 0){
                if(fbleUser.data.id != facebook_id){
                    return resolve({
                        status: 200,
                        error_code: 6,
                        message: "facebook_id hoặc access_token không chính xác",
                        data: fbleUser.data
                    });
                }

                if(user == null || user == undefined){
                    user = new TblUsers();
                    user.email = fbleUser.data.email;
                    user.name = fbleUser.data.name;
                    user.facebookId = fbleUser.data.id;
                    user.external = 3;
                    user.roleId = 12;

                    await userRepository().save(user);
                }

                const ACCESS_TOKEN = jwtService.generateAccessToken(user.id, user.roleId);
                const REFRESH_TOKEN = jwtService.generateRefreshToken(user.id, user.roleId)

                user.isLoging = 1;
                user.accessToken = ACCESS_TOKEN;
                user.refreshToken = REFRESH_TOKEN;

                await userRepository().save(user);
                
                return resolve({
                    status: 200,
                    error_code: 0,
                    message: "Xác thực thành công. TK đã được active",
                    data: {
                        id: user.id,
                        name: user.name,
                        email: user.email,
                        phone: user.phone,
                        roleId: user.roleId,
                        active: user.active,
                        avatar: user.avatar,
                        accessToken: user.accessToken,
                        is_update_info: user.isUpdateInfo,
                        contact_phone : user.contactPhone,
                        contact_phone_authened: user.contactPhoneAuthened
                      }
                });                
            }
            else{
                return resolve({
                    status: 200,
                    error_code: 5,
                    message: "facebook_id và access_token không khớp",
                    data: fbleUser.data
                });
            }
        }
        catch(ex){
            resolve({
                status: 200,
                error_code: 3,
                message: ex
            });
        }
    });    
}
