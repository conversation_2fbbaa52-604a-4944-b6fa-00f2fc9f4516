import { AppDataSource } from '@/config/config'
import {
  TblUsers,
  TblGames,
  Location,
  LocationRequirement,
  UserSpin,
  Game3UserItems,
  Game3PlaySession,
  LuckyPrize,
  SpinHistory
} from '@/entities'
import { Game3SessionStatus } from '@/entities/Game3PlaySession'
import { AuthorizedUserRequest } from '@/middlewares/auth'
import { NotFoundError, InputError } from '@/utils/ApiError'
import moment from 'moment'
import { getPrizesWithRemainingQuantity, randomPrizeWithQuantityCheck } from './gameG1.service'
import * as luckyWheelService from './luckyWheel.service'

// Helper functions
const userRepo = () => AppDataSource.getRepository(TblUsers)
const gameRepo = () => AppDataSource.getRepository(TblGames)
const locationRepo = () => AppDataSource.getRepository(Location)
const locationRequirementRepo = () => AppDataSource.getRepository(LocationRequirement)
const userSpinRepo = () => AppDataSource.getRepository(UserSpin)
const userItemsRepo = () => AppDataSource.getRepository(Game3UserItems)
const playSessionRepo = () => AppDataSource.getRepository(Game3PlaySession)
const prizeRepo = () => AppDataSource.getRepository(LuckyPrize)
const spinHistoryRepo = () => AppDataSource.getRepository(SpinHistory)

// Helper function to get current time in GMT+7
const getNowGMT7 = () => {
  return moment.utc().add(7, 'hours').toDate()
}

// Helper function to generate unique session code
const generateSessionCode = (): string => {
  return `g3_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
}

// Helper function to check if location requirements are completed
const checkLocationCompletion = (userItems: Game3UserItems[], requirements: LocationRequirement[]) => {
  const userItemsMap = new Map<string, number>()

  // Tạo map từ userItems
  userItems.forEach(item => {
    if (item.itemType.startsWith('letter_')) {
      const letter = item.itemType.replace('letter_', '')
      userItemsMap.set(`letter_${letter}`, item.quantity)
    } else {
      userItemsMap.set(item.itemType, item.quantity)
    }
  })

  // Kiểm tra từng requirement
  for (const req of requirements) {
    const key = req.itemType === 'letter' ? `letter_${req.itemName}` : req.itemType
    const userQuantity = userItemsMap.get(key) || 0

    if (userQuantity < req.requiredQuantity) {
      return false
    }
  }

  return true
}

// Helper function to calculate stars based on completion percentage
const calculateStars = (userItems: Game3UserItems[], requirements: LocationRequirement[]) => {
  let completedRequirements = 0
  const totalRequirements = requirements.length

  const userItemsMap = new Map<string, number>()
  userItems.forEach(item => {
    if (item.itemType.startsWith('letter_')) {
      const letter = item.itemType.replace('letter_', '')
      userItemsMap.set(`letter_${letter}`, item.quantity)
    } else {
      userItemsMap.set(item.itemType, item.quantity)
    }
  })

  requirements.forEach(req => {
    const key = req.itemType === 'letter' ? `letter_${req.itemName}` : req.itemType
    const userQuantity = userItemsMap.get(key) || 0

    if (userQuantity >= req.requiredQuantity) {
      completedRequirements++
    }
  })

  const completionRate = completedRequirements / totalRequirements

  if (completionRate >= 1.0) return 3
  if (completionRate >= 0.8) return 2
  if (completionRate >= 0.5) return 1
  return 0
}

// Helper function to check if user has collected all TOKYOLIFE letters
const checkTokyoLifeCompletion = (allUserItems: Game3UserItems[]) => {
  const requiredLetters = ['T', 'O', 'K', 'Y', 'O', 'L', 'I', 'F', 'E']
  const letterCounts = new Map<string, number>()

  // Đếm số lượng từng chữ cái
  allUserItems.forEach(item => {
    if (item.itemType.startsWith('letter_')) {
      const letter = item.itemType.replace('letter_', '')
      letterCounts.set(letter, (letterCounts.get(letter) || 0) + item.quantity)
    }
  })

  // Kiểm tra có đủ từng chữ cái không (cần ít nhất 1 của mỗi loại)
  for (const letter of requiredLetters) {
    if ((letterCounts.get(letter) || 0) < 1) {
      return false
    }
  }

  return true
}

// Helper function to process coupon from Bizfly (similar to gameG1)
const processCouponFromBizfly = async (
  user: any,
  prize: any,
  game: any,
  campaignId: number,
  gameId: number
) => {
  if (!prize.bizStorageId) {
    return {
      success: true,
      message: prize.name
    }
  }

  try {
    if (prize.remainingQuantity > 0) {
      const couponResponse = await luckyWheelService.getCouponFromBizfly(
        user.id,
        prize.bizStorageId,
        user.tokyoId,
        user.bizId,
        game.name,
        campaignId,
        gameId,
        gameId == 27 ? 4 : 5
      )

      if (couponResponse && couponResponse.data) {
        const couponData = {
          couponId: couponResponse.data.coupon_id,
          couponCode: couponResponse.data.code,
          couponName: couponResponse.data.name,
          qrCodeLink: couponResponse.data.link_scan_qr_code,
          end_date: couponResponse.data.end_date
        }

        return {
          success: true,
          couponData,
          message: `Bạn nhận được ${prize.name}`
        }
      }
    }

    return {
      success: false,
      message: prize.name
    }
  } catch (error) {
    console.error('Error getting coupon:', error)
    return {
      success: false,
      message: prize.name
    }
  }
}

/**
 * 1. GET /api/game/init
 * Initialize game, preload user data and game UI
 */
export const initGame = async (req: AuthorizedUserRequest) => {
  const { user } = req.authorizedUser
  const gameId = 29;

  if (!gameId) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  // Lấy thông tin game để có campaignId
  const game = await gameRepo().findOne({
    where: { id: gameId }
  })

  if (!game) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  const campaignId = game.campaignId

  // Lấy hoặc tạo user spin (progress)
  let userSpin = await userSpinRepo().findOne({
    where: { userId: user.id, campaignId, gameId }
  })

  if (!userSpin) {
    userSpin = new UserSpin()
    userSpin.userId = user.id
    userSpin.campaignId = campaignId
    userSpin.gameId = gameId
    userSpin.spinCounts = 3 // Default 3 turns for game3
    userSpin.inviteCount = 0
    userSpin.createdAt = getNowGMT7()
    userSpin.updatedAt = getNowGMT7()
    userSpin.lastRequest = getNowGMT7()
    await userSpinRepo().save(userSpin)
  }

  // Lấy danh sách locations với rewards đã nhận
  const locations = await locationRepo().find({
    where: { active: 1 },
    order: { sortOrder: 'ASC' }
  })

  const locationsWithRewards = await Promise.all(
    locations.map(async (location) => {
      // Tìm reward đã nhận cho location này (sử dụng SpinHistory)
      const reward = await spinHistoryRepo().findOne({
        where: {
          userId: user.id,
          campaignId,
          gameId,
          description: `location_${location.id}` // Dùng description để phân biệt location
        },
        relations: ['prize']
      })

      return {
        id: location.id,
        name: location.name,
        star: reward ? 1 : 0, // 1 nếu đã nhận reward, 0 nếu chưa
        received: reward ? [{
          reward_id: reward.prizeId,
          name: reward.prize?.name || 'Reward',
          image: reward.prize?.image || 'default_reward'
        }] : []
      }
    })
  )

  // Lấy tất cả letters đã thu thập từ userItems
  const allUserItems = await userItemsRepo().find({
    where: {
      userId: user.id,
      campaignId,
      gameId
    }
  })

  const collectedLetters: string[] = []
  allUserItems.forEach(item => {
    if (item.itemType.startsWith('letter_')) {
      const letter = item.itemType.replace('letter_', '')
      for (let i = 0; i < item.quantity; i++) {
        collectedLetters.push(letter)
      }
    }
  })

  return {
    status: 'success',
    user: {
      id: user.id,
      name: user.name
    },
    play_turns: userSpin.spinCounts + (userSpin.inviteCount || 0),
    letters: collectedLetters,
    locations: locationsWithRewards
  }
}

/**
 * 2. POST /api/game/rules
 * Hiển thị thể lệ chương trình
 */
export const getRules = async (locationId: number) => {
  const location = await locationRepo().findOne({
    where: { id: locationId, active: 1 }
  })

  if (!location) {
    throw new NotFoundError('Tỉnh thành không tồn tại', '', 1)
  }

  return {
    status: 'success',
    rules: location.rules || `Nội dung chi tiết dạng HTML thể lệ chương trình cho ${location.name}.`
  }
}

/**
 * 3. GET /api/game/locations
 * Danh sách các tỉnh thành có thể chọn
 */
export const getLocations = async () => {
  const locations = await locationRepo().find({
    where: { active: 1 },
    order: { sortOrder: 'ASC' }
  })

  return {
    status: 'success',
    locations: locations.map(location => ({
      id: location.id,
      name: location.name
    }))
  }
}

/**
 * 4. GET /api/game/location-detail/:id
 * Lấy yêu cầu combo vật phẩm của từng tỉnh thành
 */
export const getLocationDetail = async (locationId: number) => {
  const location = await locationRepo().findOne({
    where: { id: locationId, active: 1 }
  })

  if (!location) {
    throw new NotFoundError('Tỉnh thành không tồn tại', '', 1)
  }

  const requirements = await locationRequirementRepo().find({
    where: { locationId },
    order: { itemType: 'ASC' }
  })

  return {
    status: 'success',
    location_id: location.id,
    location_name: location.name,
    required_items: requirements.map(req => ({
      type: req.itemType,
      name: req.itemName,
      required: req.requiredQuantity
    }))
  }
}

/**
 * 5. GET /api/game/play/:location_id
 * Bắt đầu game tại 1 tỉnh thành, trả về danh sách vật phẩm & chữ cái có thể xuất hiện
 */
export const playGame = async (req: AuthorizedUserRequest, locationId: number) => {
  const { user } = req.authorizedUser
  const gameId = 29

  if (!gameId) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  // Lấy thông tin game
  const game = await gameRepo().findOne({
    where: { id: gameId }
  })

  if (!game) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  const campaignId = game.campaignId

  // Kiểm tra location có tồn tại
  const location = await locationRepo().findOne({
    where: { id: locationId, active: 1 }
  })

  if (!location) {
    throw new NotFoundError('Tỉnh thành không tồn tại', '', 1)
  }

  // Lấy user spin (progress)
  const userSpin = await userSpinRepo().findOne({
    where: { userId: user.id, campaignId, gameId }
  })

  const totalTurns = (userSpin?.spinCounts || 0) + (userSpin?.inviteCount || 0)
  if (!userSpin || totalTurns <= 0) {
    throw new InputError('Bạn đã hết lượt chơi', '', 1)
  }

  // Tạo session code
  const sessionCode = generateSessionCode()

  // Lấy danh sách requirements cho location này
  const requirements = await locationRequirementRepo().find({
    where: { locationId: locationId }
  })

  // Phân loại items và letters từ requirements
  const spawnItems = requirements
    .filter(requirement => requirement.itemType !== 'letter')
    .map(requirement => ({
      type: requirement.itemType,
      image: `item_${requirement.itemType}`
    }))

  const spawnLetters = requirements
    .filter(requirement => requirement.itemType === 'letter')
    .map(requirement => requirement.itemName)

  // Tạo play session
  const playSession = new Game3PlaySession()
  playSession.code = sessionCode
  playSession.userId = user.id
  playSession.campaignId = campaignId
  playSession.gameId = gameId
  playSession.locationId = locationId
  playSession.spawnItems = spawnItems
  playSession.spawnLetters = spawnLetters
  playSession.expiresAt = moment().add(10, 'minutes').toDate() // Session expires in 10 minutes
  playSession.createdAt = getNowGMT7()
  playSession.updatedAt = getNowGMT7()

  await playSessionRepo().save(playSession)

  const remainingTurns = userSpin.spinCounts + userSpin.inviteCount

  return {
    status: 'success',
    play_turns: remainingTurns,
    spawn_items: spawnItems,
    spawn_letters: spawnLetters,
    code: sessionCode,
    location: {
      id: location.id,
      name: location.name,
      rules: location.rules
    }
  }
}

/**
 * 6. POST /api/game/end
 * Kết thúc lượt chơi, xác nhận số vật phẩm và chữ cái thu thập được
 */
interface EndGameData {
  code: string
  location_id: number
  duration: number
  distance: number
  collected_items: Record<string, number>
  collected_letters: string[]
  context?: string
}

export const endGame = async (req: AuthorizedUserRequest, endGameData: EndGameData) => {
  const { user } = req.authorizedUser
  const gameId = 29;
  const { code, location_id: locationId, duration, distance, collected_items: collectedItems, collected_letters: collectedLetters, context } = endGameData

  if (!gameId) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  // Lấy thông tin game
  const game = await gameRepo().findOne({
    where: { id: gameId }
  })

  if (!game) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  const campaignId = game.campaignId

  // Kiểm tra session có tồn tại và hợp lệ
  const playSession = await playSessionRepo().findOne({
    where: {
      code,
      userId: user.id,
      campaignId,
      gameId,
      locationId: locationId,
      status: Game3SessionStatus.ACTIVE
    }
  })

  if (!playSession) {
    throw new InputError('Phiên chơi không hợp lệ hoặc đã hết hạn', '', 1)
  }

  // Kiểm tra session có hết hạn chưa
  if (new Date() > playSession.expiresAt) {
    throw new InputError('Phiên chơi đã hết hạn', '', 1)
  }

  // Lấy user spin (progress)
  const userSpin = await userSpinRepo().findOne({
    where: { userId: user.id, campaignId, gameId }
  })

  if (!userSpin) {
    throw new NotFoundError('Không tìm thấy tiến trình game của user', '', 1)
  }

  // Lấy danh sách requirements cho location này để validation
  const locationRequirements = await locationRequirementRepo().find({
    where: { locationId: locationId }
  })

  // Tạo map để kiểm tra nhanh
  const validItems = new Set(
    locationRequirements
      .filter(requirement => requirement.itemType !== 'letter')
      .map(requirement => requirement.itemType)
  )

  const validLetters = new Set(
    locationRequirements
      .filter(requirement => requirement.itemType === 'letter')
      .map(requirement => requirement.itemName)
  )

  // Validation collected_items
  if (collectedItems) {
    for (const itemType of Object.keys(collectedItems)) {
      if (!validItems.has(itemType)) {
        throw new InputError(`Item '${itemType}' không tồn tại trong location ${locationId}`, '', 1)
      }
    }
  }

  // Validation collected_letters
  if (collectedLetters && Array.isArray(collectedLetters)) {
    for (const letter of collectedLetters) {
      if (!validLetters.has(letter)) {
        throw new InputError(`Letter '${letter}' không tồn tại trong location ${locationId}`, '', 1)
      }
    }
  }

  // Cập nhật items cho user
  if (collectedItems) {
    for (const [itemType, quantity] of Object.entries(collectedItems)) {
      if (typeof quantity === 'number' && quantity > 0) {
        let userItem = await userItemsRepo().findOne({
          where: {
            userId: user.id,
            campaignId,
            gameId,
            locationId: locationId,
            itemType
          }
        })

        if (!userItem) {
          userItem = new Game3UserItems()
          userItem.userId = user.id
          userItem.campaignId = campaignId
          userItem.gameId = gameId
          userItem.locationId = locationId
          userItem.itemType = itemType
          userItem.quantity = quantity
          userItem.createdAt = getNowGMT7()
        } else {
          userItem.quantity += quantity
        }

        userItem.updatedAt = getNowGMT7()
        await userItemsRepo().save(userItem)
      }
    }
  }

  // Cập nhật letters cho user - lưu như items
  if (collectedLetters && Array.isArray(collectedLetters)) {
    for (const letter of collectedLetters) {
      let userItem = await userItemsRepo().findOne({
        where: {
          userId: user.id,
          campaignId,
          gameId,
          locationId: locationId,
          itemType: `letter_${letter}` // Phân biệt từng chữ cái
        }
      })

      if (!userItem) {
        userItem = new Game3UserItems()
        userItem.userId = user.id
        userItem.campaignId = campaignId
        userItem.gameId = gameId
        userItem.locationId = locationId
        userItem.itemType = `letter_${letter}`
        userItem.quantity = 1
        userItem.createdAt = getNowGMT7()
      } else {
        userItem.quantity += 1
      }

      userItem.updatedAt = getNowGMT7()
      await userItemsRepo().save(userItem)
    }
  }

  // Trừ 1 lượt chơi (ưu tiên trừ inviteCount trước)
  if (userSpin.inviteCount > 0) {
    userSpin.inviteCount -= 1
  } else if (userSpin.spinCounts > 0) {
    userSpin.spinCounts -= 1
  }
  userSpin.updatedAt = getNowGMT7()
  await userSpinRepo().save(userSpin)

  // Đánh dấu session đã hoàn thành
  playSession.status = Game3SessionStatus.COMPLETED
  playSession.updatedAt = getNowGMT7()
  await playSessionRepo().save(playSession)

  // Lấy tổng items hiện tại của user cho location này
  const userItems = await userItemsRepo().find({
    where: {
      userId: user.id,
      campaignId,
      gameId,
      locationId: locationId
    }
  })

  const totalItems: Record<string, number> = {}
  const collectedLettersFromItems: string[] = []

  userItems.forEach(item => {
    if (item.itemType.startsWith('letter_')) {
      // Lấy chữ cái từ itemType
      const letter = item.itemType.replace('letter_', '')
      for (let i = 0; i < item.quantity; i++) {
        collectedLettersFromItems.push(letter)
      }
    } else {
      totalItems[item.itemType] = item.quantity
    }
  })

  const remainingTurns = userSpin.spinCounts + (userSpin.inviteCount || 0)

  // Kiểm tra và tặng quà
  let locationReward = null
  let tokyoLifeReward = null
  let stars = 0

  // 1. Kiểm tra hoàn thành location
  const isLocationCompleted = checkLocationCompletion(userItems, locationRequirements)
  if (isLocationCompleted) {
    stars = 3 // Hoàn thành = 3 sao

    try {
      // Lấy prizes cho location reward
      const prizes = await getPrizesWithRemainingQuantity(campaignId, gameId, context || '')
      if (prizes.length > 0) {
        const selectedPrize = randomPrizeWithQuantityCheck(prizes)
        if (selectedPrize) {
          const couponResult = await processCouponFromBizfly(user, selectedPrize, game, campaignId, gameId)

          // Lưu vào SpinHistory
          await luckyWheelService.saveSpinHistory(
            user.id,
            selectedPrize.id,
            campaignId,
            gameId,
            couponResult.couponData || null
          )

          locationReward = {
            prize: selectedPrize,
            coupon: couponResult.couponData || null,
            message: couponResult.message
          }
        }
      }
    } catch (error) {
      console.error('Error giving location reward:', error)
    }
  } else {
    // Tính sao dựa trên tỉ lệ hoàn thành
    stars = calculateStars(userItems, locationRequirements)
  }

  // 2. Kiểm tra hoàn thành toàn bộ chữ TOKYOLIFE
  const allUserItemsForGame = await userItemsRepo().find({
    where: {
      userId: user.id,
      campaignId,
      gameId
    }
  })

  const isTokyoLifeCompleted = checkTokyoLifeCompletion(allUserItemsForGame)
  if (isTokyoLifeCompleted) {
    try {
      // Lấy prizes cho TOKYOLIFE reward
      const prizes = await getPrizesWithRemainingQuantity(campaignId, gameId, context || '')
      if (prizes.length > 0) {
        const selectedPrize = randomPrizeWithQuantityCheck(prizes)
        if (selectedPrize) {
          const couponResult = await processCouponFromBizfly(user, selectedPrize, game, campaignId, gameId)

          // Lưu vào SpinHistory
          await luckyWheelService.saveSpinHistory(
            user.id,
            selectedPrize.id,
            campaignId,
            gameId,
            couponResult.couponData || null
          )

          tokyoLifeReward = {
            prize: selectedPrize,
            coupon: couponResult.couponData || null,
            message: couponResult.message
          }
        }
      }
    } catch (error) {
      console.error('Error giving TOKYOLIFE reward:', error)
    }
  }

  return {
    status: 'success',
    play_turns: remainingTurns,
    time_left: duration,
    distance_covered: distance,
    collected_items: totalItems,
    collected_letters: collectedLettersFromItems,
    stars: stars,
    location_completed: isLocationCompleted,
    tokyo_life_completed: isTokyoLifeCompleted,
    rewards: {
      location_reward: locationReward,
      tokyo_life_reward: tokyoLifeReward
    }
  }
}
