import express from 'express'
import { AuthorizedUser } from '@/types/models/auth'
import { JsonWebTokenError, NotBeforeError, TokenExpiredError } from 'jsonwebtoken'
import { jwtService , commonService} from '@/services'
import { TokenType } from '@/types/models/token'
import { AppDataSource } from '@/config/config'
import { TblUsers } from '@/entities'
import ApiError from '@/utils/ApiError'
import { UserStatus } from '@/entities/TblUsers'
import {pino} from 'pino';

const logger = pino({
  level: 'info',
  transport: {
    target: 'pino-pretty',
    options: { colorize: true }
  }
});

async function logRequest(req) {
  try{
    //try to get refresh_token
    console.log(`get zaloRefreshToken`);

    //log request
    logger.info({
      method: req.method,
      url: req.url,
      query: req.query,
      body: req.body,
      param: req.params,
      headers: req.headers
    }, 'Incoming request');
  }
  catch(ex){
    console.log(`write log error: ${ex}`);
  }
}

const userRepository = () => AppDataSource.getRepository(TblUsers);

export interface AuthorizedUserRequest extends express.Request {
  authorizedUser: AuthorizedUser
}

const getToken = (req) => {
  if (req.headers.authorization) {
    const authorization = req.headers.authorization.split(' ')
    if (authorization.length === 2 && authorization[0] === 'Bearer') {
      return authorization[1]
    }
  }
  return null
}

export const noAuthenticate: express.RequestHandler  = async (req, res, next) => {  
  logRequest(req) ;

  next();
}

const authenticateByUid = async (userId: number): Promise<AuthorizedUser> => {
  // 1. find user
  // const user = await userRepository().findOne({ where: { id: userId, deletedAt: IsNull() } })
  const user = await userRepository().createQueryBuilder('user').where({ id: userId }).getOne()
  
  if (!user) {
    // User authentication failure_user not found
    throw new ApiError(200, 'EUSER404', 'User not found')
  }

  // check active account
  if (user.active !== UserStatus.ACTIVE) {
    // user account is not active
    throw new ApiError(200, 'E102', 'User account is not active')
  }

  return { user }
}

// export const authenticate: express.RequestHandler = async (req, res, next) => {
export const authenticate: express.RequestHandler = async (req: AuthorizedUserRequest, res, next) => {
  try {
    logRequest(req) ;

    const token = getToken(req);

    //console.log(`AuthorizedUserRequest: ${req.url} - ${req.params.id} - ${req.method}`);
    
    if (token === null) {
      // token empty
      //throw new ApiError(200, 'EAUTH400', 'Token empty')
      return res.status(200).json({status: 200, code: "EAUTH400", "error_code":"1", message: "Token empty"})
    }
    // token verify
    const payload = jwtService.verifyAuthenticationToken(token);

    if (!payload) {
      //throw new ApiError(200, 'EAUTH40111', 'EAUTH401','Token error')
      return res.status(200).json({status: 200, code: "EAUTH401", "error_code":"1", message: "Token error"})
    }

    if (payload.typ !== TokenType.AccessToken) {
      // Token type error
      //throw new ApiError(200, 'EAUTH402', 'Token type error')
      return res.status(200).json({status: 200, code: "EAUTH402", "error_code":"1", message: "Token type error"})
    }
    
    //const authorizedUser = await authService.authenticate(req, res, payload.id);
    const authorizedUser = await authenticateByUid(payload.id);

    //console.log(`PAYLOAD_ID: ${payload.id}`);

    req.authorizedUser = authorizedUser;

    next();
  } catch (e) {
    switch (e.constructor) {
      case JsonWebTokenError:{
          return res.status(200).json({status: 200, code: "EAUTH402", "error_code":"1", message: "Token error"});
      }
      case TokenExpiredError:
        return res.status(200).json({status: 200,code: "EAUTH402", "error_code":"1", message: "Token expired"});
      case NotBeforeError:
        //next(new ApiError(200, 'EAUTH403', 'Token not before'))
        return res.status(200).json({status: 200,code: "EAUTH403", "error_code":"1", message: "Token not before"});
      default:
        next(e)
    }
  }
}

export const authenticateUpdate: express.RequestHandler = async (req: AuthorizedUserRequest, res, next) => {
  try {
    logRequest(req) ;

    const token = getToken(req);
    const uid = req.params.id;

    //console.log(`AuthorizedUserRequest: ${req.url} - ${req.params.id} - ${req.method}`);
    
    if (token === null) {
      // token empty
      //throw new ApiError(200, 'EAUTH400', 'Token empty')
      return res.status(200).json({status: 200, code: "EAUTH400", "error_code":"1", message: "Token empty"});
    }
    // token verify
    const payload = jwtService.verifyAuthenticationToken(token);

    if (!payload) {
      //throw new ApiError(200, 'EAUTH40111', 'EAUTH401','Token error')
      return res.status(200).json({status: 200, code: "EAUTH401", "error_code":"1", message: "Token error"});
    }

    if(uid != payload.id.toString()){
      return res.status(200).json({status: 200, code: "EAUTH407", "error_code":"7", message: "Token is not matched"});
    }

    if (payload.typ !== TokenType.AccessToken) {
      // Token type error
      //throw new ApiError(200, 'EAUTH402', 'Token type error')
      return res.status(200).json({status: 200, code: "EAUTH402", "error_code":"1", message: "Token type error"});
    }
    
    //const authorizedUser = await authService.authenticate(req, res, payload.id);
    const authorizedUser = await authenticateByUid(payload.id);

    //console.log(`PAYLOAD_ID: ${payload.id}`);

    req.authorizedUser = authorizedUser;

    next();
  } catch (e) {
    switch (e.constructor) {
      case JsonWebTokenError:{
          return res.status(200).json({status: 200, code: "EAUTH402", "error_code":"1", message: "Token error"});
      }
      case TokenExpiredError:
        return res.status(200).json({status: 200,code: "EAUTH402", "error_code":"1", message: "Token expired"});
      case NotBeforeError:
        //next(new ApiError(200, 'EAUTH403', 'Token not before'))
        return res.status(200).json({status: 200,code: "EAUTH403", "error_code":"1", message: "Token not before"});
      default:
        next(e)
    }
  }
}

export const hasRoles = (roles) => async (req, res, next) => {
  if (typeof roles === 'string') roles = [roles]

  // if (!req.authorizedUser) next(new ApiError(403, 'EAUTH403', 'Forbidden'))
  // else {
  //   const { user } = req.authorizedUser
  //   !roles.includes(user.role) ? next(new ApiError(403, 'EAUTH403', 'Forbidden')) : next()
  // }
}
