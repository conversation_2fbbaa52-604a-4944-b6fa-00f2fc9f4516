# Tóm tắt thay đổi luật chơi Game G2

## Yêu cầu ban đầu:
1. Mỗi khi claimVoucher thành công thì cộng 1 lượt vào totalSpinToday và lưu lại box đã gửi lên
2. <PERSON><PERSON> init thì trả về các box đã từng mở và thông tin phần quà
3. <PERSON><PERSON><PERSON><PERSON> chia sẻ cộng thêm sẽ đánh dấu là lượt chia sẻ is_share_turn
4. Thêm field để lưu các box đã gửi lên

## Các thay đổi đã thực hiện:

### 1. Database Schema Changes
**File**: `src/entities/UserSpin.ts`
- Thêm field `openedBoxes: string | null` để lưu JSON string các box đã mở

**File**: `src/migrations/add_opened_boxes_to_user_spins.sql`
- Migration để thêm column `opened_boxes` vào bảng `user_spins`

### 2. Game Logic Changes

#### A. processG2Game (Game G2 claim logic)
**File**: `src/services/gameG1.service.ts`
- Cộng `totalSpinToday += 1` khi claimVoucher thành công (có voucher được nhận)
- Lưu các box đã gửi lên vào `openedBoxes` (JSON string)
- Merge với các box đã mở trước đó, loại bỏ duplicate

#### B. processG1Game (Game G1 claim logic)
**File**: `src/services/gameG1.service.ts`
- Cộng `totalSpinToday += 1` khi claim voucher thành công (có couponData)

#### C. initGame (Game initialization)
**File**: `src/services/gameG1.service.ts`
- Reset `totalSpinToday = 0` và `openedBoxes = null` khi sang ngày mới
- Trả về `opened_boxes` và `received_rewards` cho game G2
- Cải thiện logic reset hàng ngày

#### D. getShareTurnByGameId (Share turn logic)
**File**: `src/services/gameG1.service.ts`
- Sử dụng `totalSpinToday` thay vì `totalPlayTurns` để xác định lượt chia sẻ
- Trả về true nếu đã chơi nhiều hơn số lượt mặc định (G2: 3 lượt)

#### E. shareForExtraTurn (Share for extra turn)
**File**: `src/services/gameG1.service.ts`
- Cộng `totalSpinToday += 1` khi chia sẻ thành công
- Trả về `is_share_turn: true` trong response

#### F. processNoTurns (No turns remaining)
**File**: `src/services/gameG1.service.ts`
- Trả về `opened_boxes` và `received_rewards` khi hết lượt chơi G2
- Cập nhật interface `NoTurnsResult` để bao gồm `opened_boxes`

### 3. API Response Changes

#### initGame Response (Game G2):
```json
{
  "status": "success",
  "user": {...},
  "play_turns": 3,
  "is_share_turn": false,
  "first_play": true,
  "opened_boxes": [1, 2, 3],
  "received_rewards": [
    {
      "id": 1,
      "name": "Voucher 100K",
      "image": "voucher100k.png",
      "spin_time": "2024-01-01T10:00:00Z"
    }
  ]
}
```

#### shareForExtraTurn Response:
```json
{
  "status": "success",
  "message": "Bạn đã nhận thêm 1 lượt chơi!",
  "play_turns": 4,
  "is_share_turn": true
}
```

#### processNoTurns Response (Game G2):
```json
{
  "status": "no_turns",
  "message": "Bạn đã hết lượt chơi hôm nay",
  "boxes": [...],
  "opened_boxes": [1, 2, 3],
  "received_rewards": [...],
  "share_remaining": 1
}
```

## Logic Flow:

### 1. Khi user chơi game G2:
1. User gửi `box_ids` để mở box
2. Nếu có winning boxes và nhận được voucher thành công:
   - `totalSpinToday += 1`
   - Lưu `box_ids` vào `openedBoxes`
3. Trả về kết quả với boxes và rewards

### 2. Khi user init game:
1. Kiểm tra nếu sang ngày mới: reset `totalSpinToday = 0`, `openedBoxes = null`
2. Tính `is_share_turn` dựa trên `totalSpinToday > default_turns`
3. Với game G2: trả về `opened_boxes` và `received_rewards`

### 3. Khi user share:
1. Cộng thêm 1 lượt chơi
2. `totalSpinToday += 1` (đánh dấu lượt chia sẻ)
3. Trả về `is_share_turn: true`

## Database Migration:
```sql
ALTER TABLE `user_spins` 
ADD COLUMN `opened_boxes` TEXT NULL COMMENT 'JSON string storing array of box IDs that user has opened in game G2';
```

## Test Cases:
1. Test claim voucher thành công → totalSpinToday tăng
2. Test claim voucher thất bại → totalSpinToday không tăng
3. Test init game G2 → trả về opened_boxes và received_rewards
4. Test share → totalSpinToday tăng, is_share_turn = true
5. Test reset hàng ngày → totalSpinToday = 0, openedBoxes = null
6. Test is_share_turn logic với các giá trị totalSpinToday khác nhau
