# Test Guest Game G2 Changes

## <PERSON><PERSON><PERSON> thay đổi đã thực hiện:

### 1. Thêm field `openedBoxes` vào GuestGameSession entity
- **File**: `src/entities/GuestGameSession.ts`
- **Migration**: `src/migrations/add_opened_boxes_to_guest_sessions.sql`

### 2. Cập nhật processGuestG2Game
- **File**: `src/api/guestGame.api.ts`
- **Thay đổi**: <PERSON><PERSON><PERSON> thông tin opened boxes với reward tương ứng

### 3. Cập nhật mergeGuestDataToUser
- **File**: `src/services/gameG1.service.ts`
- **Thay đổi**: Merge opened boxes từ guest session vào UserSpin

## Test Cases:

### 1. Test Guest Init G2
```
GET /api/guest-game/g2/init
Expected response:
{
  "status": "success",
  "guest_session_id": "uuid-string",
  "play_turns": 1
}
```

### 2. Test Guest Claim G2
```
POST /api/guest-game/g2/claim
Body: {
  "guest_session_id": "uuid-string",
  "box_ids": [1, 2, 3]
}
Expected response:
{
  "status": "success",
  "message": "Bạn đã mở hộp quà số 1! Đăng nhập để nhận phần thưởng.",
  "guest_session_id": "uuid-string",
  "boxes": [...],
  "play_turns": 0
}

Expected in database (guest_game_sessions.openedBoxes):
[
  {
    "box_id": 1,
    "reward": {
      "id": 123,
      "name": "Voucher 100K",
      "image": "voucher.png",
      "type": "voucher",
      "has_voucher": true
    },
    "opened_at": "2024-01-01T10:00:00Z"
  },
  {
    "box_id": 2,
    "reward": {
      "id": null,
      "name": "Chúc bạn may mắn lần sau!",
      "image": "chuc_mm",
      "type": "lose",
      "has_voucher": false
    },
    "opened_at": "2024-01-01T10:00:00Z"
  }
]
```

### 3. Test Merge Guest Data
```
POST /api/game/merge-guest-data
Headers: Authorization: Bearer <user-token>
Body: {
  "guest_session_id": "uuid-string"
}
Expected response:
{
  "status": "success",
  "message": "Đã merge dữ liệu guest thành công",
  "data": {
    "guest_session_id": "uuid-string",
    "user_id": 12345,
    "processed_coupons": [...],
    "errors": []
  }
}

Expected in database:
1. guest_game_sessions.isMerged = true
2. guest_game_sessions.mergedUserId = user_id
3. user_spins.openedBoxes chứa dữ liệu từ guest
4. user_spins.totalSpinToday += 1
5. spin_histories có record cho các voucher
```

### 4. Test User Init G2 sau khi merge
```
GET /api/game/g2/init
Headers: Authorization: Bearer <user-token>
Expected response:
{
  "status": "success",
  "user": {...},
  "play_turns": 3,
  "is_share_turn": false,
  "first_play": false,
  "opened_boxes": [1, 2],
  "box_rewards": [
    {
      "box_id": 1,
      "reward": {
        "id": 123,
        "name": "Voucher 100K",
        "image": "voucher.png",
        "type": "voucher",
        "has_voucher": true
      },
      "opened_at": "2024-01-01T10:00:00Z"
    },
    {
      "box_id": 2,
      "reward": {
        "id": null,
        "name": "Chúc bạn may mắn lần sau!",
        "image": "chuc_mm",
        "type": "lose",
        "has_voucher": false
      },
      "opened_at": "2024-01-01T10:00:00Z"
    }
  ]
}
```

## Kiểm tra Database:

### 1. Bảng guest_game_sessions
```sql
SELECT 
  guestSessionId,
  gameId,
  hasPlayed,
  isMerged,
  mergedUserId,
  JSON_PRETTY(prizesWon) as prizes,
  JSON_PRETTY(openedBoxes) as boxes
FROM guest_game_sessions 
WHERE gameId = 28;
```

### 2. Bảng user_spins
```sql
SELECT 
  userId,
  gameId,
  totalSpinToday,
  JSON_PRETTY(openedBoxes) as boxes
FROM user_spins 
WHERE gameId = 28;
```

### 3. Bảng spin_histories
```sql
SELECT 
  userId,
  prizeId,
  voucherCode,
  voucherName,
  spinTime
FROM spin_histories 
WHERE gameId = 28 
ORDER BY spinTime DESC;
```

## Lưu ý quan trọng:

1. **Guest chỉ chơi được 1 lần**: hasPlayed = true sau khi claim
2. **Merge chỉ được 1 lần**: isMerged = true sau khi merge
3. **User không được chơi trước khi merge**: checkUserPlayedGane phải return false
4. **totalSpinToday được cộng đúng**: +1 từ guest session
5. **openedBoxes được merge đúng**: append vào array hiện tại
6. **SpinHistory được tạo đúng**: cho các voucher có bizStorageId

## Flow hoàn chỉnh:

1. Guest init G2 → Tạo guest session
2. Guest claim G2 → Lưu prizesWon và openedBoxes
3. User đăng nhập → Gọi merge API
4. Merge thành công → Tạo SpinHistory, cập nhật UserSpin
5. User init G2 → Trả về dữ liệu đã merge
6. User tiếp tục chơi → Dữ liệu được append vào openedBoxes hiện tại
