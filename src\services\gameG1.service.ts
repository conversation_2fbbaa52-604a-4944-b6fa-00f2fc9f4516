import { AppDataSource } from '@/config/config'
import { UserSpin, LuckyPrize, SpinHistory, TblUsers, TblGames, VoucherDraw, InviteHistory, UserGiftAward, GuestGameSession } from '@/entities'
import { AuthorizedUserRequest } from '@/middlewares/auth'
import { luckyWheelService } from '@/services'
import { NotFoundError } from '@/utils/ApiError'
import moment from 'moment'
import { v4 as uuidv4 } from 'uuid'

// Helper functions
const userSpinRepo = () => AppDataSource.getRepository(UserSpin)
const prizeRepo = () => AppDataSource.getRepository(LuckyPrize)
const spinHistoryRepo = () => AppDataSource.getRepository(SpinHistory)
const gameRepo = () => AppDataSource.getRepository(TblGames)
const voucherDrawRepo = () => AppDataSource.getRepository(VoucherDraw)
const userRepo = () => AppDataSource.getRepository(TblUsers)
const inviteHistoryRepo = () => AppDataSource.getRepository(InviteHistory)
const userGiftAwardRepo = AppDataSource.getRepository(UserGiftAward)
const guestGameSessionRepo = () => AppDataSource.getRepository(GuestGameSession)

// Helper function to get current time in GMT+7
const getNowGMT7 = () => {
  return moment.utc().add(7, 'hours').toDate();
}

// Helper function to get default spin counts by gameId
const getDefaultSpinCountsByGameId = (gameId: number): number => {
  const spinCountsMap: Record<number, number> = {
    27: 2,  // Game G1
    28: 3,  // Game G2
    29: 5,  // Game G3
    // Có thể thêm mapping cho các game khác tại đây
  }
  
  return spinCountsMap[gameId] || 1 // Mặc định 1 lượt nếu không có mapping
}

const getShareTurnByGameId = (gameId: number, totalSpinToday: number): boolean => {
  const spinCountsMap: Record<number, number> = {
    27: 2,  // Game G1
    28: 3,  // Game G2
  }

  return totalSpinToday > (spinCountsMap[gameId] || 1) // Trả về true nếu đã chơi nhiều hơn số lượt mặc định
}

// Helper function to convert gameId slug to number
export const convertGameIdSlugToNumber = (gameIdSlug: string): number => {
  switch (gameIdSlug) {
    case 'g1':
      return 27;
    case 'g2':
      return 28;
    case 'g3':
      return 29;
    // Thêm các case khác nếu cần
    default:
      return parseInt(gameIdSlug) || 0
  }
}

// Helper function to check if it's same day (Vietnam timezone)
const isSameDay = (date1: Date, date2: Date): boolean => {
  const d1 = new Date(date1.getTime() - (7 * 3600000)) // Convert to GMT+7
  const d2 = new Date(date2.getTime() - (7 * 3600000)) // Convert to GMT+7
  return d1.toDateString() === d2.toDateString()
}

// Helper function to calculate total play turns
const getTotalPlayTurns = (userSpin: any): number => {
  return (userSpin.spinCounts || 0) + (userSpin.inviteCount || 0)
}

// Helper function to deduct play turns (prioritize inviteCount first)
const deductPlayTurn = (userSpin: any): void => {
  if (userSpin.inviteCount > 0) {
    userSpin.inviteCount -= 1
  } else if (userSpin.spinCounts > 0) {
    userSpin.spinCounts -= 1
  }
}

// Helper function to filter prizes that can be randomly selected
const getEligiblePrizesForRandom = (prizes: any[]) => {
  const eligible = prizes.filter(prize => 
    prize.type === 'lose' || // Luôn có thể chọn lose prizes
    (['voucher', 'spin'].includes(prize.type) && prize.remainingQuantity > 0) // Chỉ chọn voucher còn số lượng
  )
  return eligible
}

// Enhanced randomPrize function that respects remaining quantity and winrate
const randomPrizeWithQuantityCheck = (prizes: any[]): any | null => {
  const eligiblePrizes = getEligiblePrizesForRandom(prizes)
  
  if (eligiblePrizes.length === 0) {
    return null
  }
  
  // Tính tổng winrate của các prizes còn lại
  let total = eligiblePrizes.reduce((sum, p) => sum + (p.winrate || 0), 0)
  console.log('total', total);
  console.log('eligiblePrizes', eligiblePrizes);
  if (total === 0) {
    // Nếu không có winrate nào được set, chọn random đều
    const randomIndex = Math.floor(Math.random() * eligiblePrizes.length)
    return eligiblePrizes[randomIndex]
  }

  // Nếu total < 100 thì kiểm tra các tổng winrate của các prize có type = voucher và set lại winrate cho các prize có type = lose
  if (total < 100) {
    const voucherPrizes = eligiblePrizes.filter(p => p.type === 'voucher')
    const losePrizes = eligiblePrizes.filter(p => p.type === 'lose')
    
    if (voucherPrizes.length > 0) {
        const totalVoucherWinrate = voucherPrizes.reduce((sum, p) => sum + (p.winrate || 0), 0)
        const remainingWinrate = 100 - totalVoucherWinrate
        if (losePrizes.length > 0) {
          const newLoseWinrate = remainingWinrate / losePrizes.length;
          // cập nhật lại total và eligiblePrizes theo thay đổi winrate của lose prizes
          eligiblePrizes.forEach(p => {
            if (p.type === 'lose') {
              p.winrate = newLoseWinrate
            }
          })
        }
        console.log('losePrizes', losePrizes)
    }
    
    total = eligiblePrizes.reduce((sum, p) => sum + (p.winrate || 0), 0)
  }
  
  // Random theo tỉ lệ winrate
  let rand = Math.random() * total
  
  for (const prize of eligiblePrizes) {
    const prizeWinrate = prize.winrate || 0
    if (rand < prizeWinrate) {
      return prize
    }
    rand -= prizeWinrate
  }
  
  // Fallback: trả về prize cuối cùng nếu có lỗi tính toán
  return eligiblePrizes[eligiblePrizes.length - 1]
}

// Helper function to get prizes with remaining quantity
const getPrizesWithRemainingQuantity = async (campaignId: number, gameId: number, context: string) => {
  let prizes = [];
  if(!context || context == '' || context == '0') {
    prizes = await luckyWheelService.getActivePrizesNotContext(campaignId, gameId)
  }else{
    prizes = await luckyWheelService.getActivePrizesContext(campaignId, gameId, context)
  }

  if (prizes.length === 0) {
    prizes = await luckyWheelService.getActivePrizesNotContext(campaignId, gameId);
  }
  // Batch query để lấy số lượng đã trúng cho tất cả prizes cùng lúc
  const voucherPrizeIds = prizes
    .filter(prize => ['voucher', 'spin'].includes(prize.type) && prize.bizStorageId)
    .map(prize => prize.id)

  const wonCountMap = new Map<number, number>()

  if (voucherPrizeIds.length > 0) {
    // Single query để lấy tất cả won counts thay vì N queries riêng lẻ
    const wonCounts = await spinHistoryRepo()
      .createQueryBuilder('spin_history')
      .select('spin_history.prizeId', 'prizeId')
      .addSelect('COUNT(*)', 'count')
      .where('spin_history.prizeId IN (:...prizeIds)', { prizeIds: voucherPrizeIds })
      .groupBy('spin_history.prizeId')
      .getRawMany()
    console.log('wonCounts', wonCounts);
    // Tạo map để lookup nhanh O(1)
    wonCounts.forEach(item => {
      const prizeId = parseInt(item.prizeId)
      const count = parseInt(item.count)
      wonCountMap.set(prizeId, count)
    })
  }

  // Tính remainingQuantity cho từng prize (không cần Promise.all vì không có async operations)
  const prizesWithRemaining = prizes.map(prize => {
    let remainingQuantity = prize.quantity

    // Chỉ tính số lượng đã trúng cho voucher
    if (['voucher', 'spin'].includes(prize.type) && prize.bizStorageId) {
      const wonCount = wonCountMap.get(prize.id) || 0
      remainingQuantity = prize.quantity - wonCount
    }

    return {
      ...prize,
      remainingQuantity
    }
  })
  console.log('prizesWithRemaining', prizesWithRemaining);
  return prizesWithRemaining
}

// Module xử lý coupon từ Bizfly
interface CouponData {
  couponId: number
  couponCode: string
  couponName: string
  qrCodeLink: string
  end_date: string
}

interface CouponResult {
  success: boolean
  couponData?: CouponData
  replacementPrize?: any
  message: string
}

const processCouponFromBizfly = async (
  user: any,
  prize: any,
  prizes: any[],
  game: any,
  campaignId: number,
  gameId: number
): Promise<CouponResult> => {
  if (!prize.bizStorageId) {
    return {
      success: false,
      message: prize.name
    }
  }

  try {
    // Kiểm tra remainingQuantity đã được tính sẵn
    if (prize.remainingQuantity > 0) {
      const couponResponse = await luckyWheelService.getCouponFromBizfly(
        user.id,
        prize.bizStorageId,
        user.tokyoId,
        user.bizId,
        game.name,
        campaignId,
        gameId,
        gameId == 27 ? 4 : 5
      )

      if (couponResponse && couponResponse.data) {
        const couponData: CouponData = {
          couponId: couponResponse.data.coupon_id,
          couponCode: couponResponse.data.code,
          couponName: couponResponse.data.name,
          qrCodeLink: couponResponse.data.link_scan_qr_code,
          end_date: couponResponse.data.end_date
        }

        return {
          success: true,
          couponData,
          message: `Bạn nhận được ${prize.name}`
        }
      } else {
        // Nếu API trả về false, tìm prize có type = 'lose' để thay thế
        const losePrize = prizes.find(p => p.type === 'lose')
        return {
          success: false,
          replacementPrize: losePrize,
          message: losePrize ? losePrize.name : 'Chúc bạn may mắn lần sau!'
        }
      }
    } else {
      // Trường hợp này không nên xảy ra vì randomPrizeWithQuantityCheck đã lọc rồi
      // Nhưng để an toàn, tìm prize có type = 'lose' để thay thế
      const losePrize = prizes.find(p => p.type === 'lose')
      return {
        success: false,
        replacementPrize: losePrize,
        message: losePrize ? losePrize.name : 'Chúc bạn may mắn lần sau!'
      }
    }
  } catch (error) {
    console.error('Error getting coupon:', error)
    return {
      success: false,
      message: 'Chúc bạn may mắn lần sau!'
    }
  }
}

// Module xử lý prize type 'spin' - lưu vào VoucherDraw
const saveVoucherDrawForSpinPrize = async (
  userId: number,
  campaignId: number,
  gameId: number,
  couponData: CouponData
): Promise<void> => {
  try {
    // Lấy thông tin user để có số điện thoại
    const user = await userRepo().findOne({ where: { id: userId } })
    const userPhone = user?.phone || ''

    const voucherDraw = new VoucherDraw()
    voucherDraw.userId = userId
    voucherDraw.campaignId = campaignId
    voucherDraw.gameId = gameId
    voucherDraw.phone = userPhone
    voucherDraw.voucherCode = couponData.couponCode
    voucherDraw.storageId = couponData.couponId.toString()
    voucherDraw.createdAt = getNowGMT7()

    await voucherDrawRepo().save(voucherDraw)
    console.log('Đã lưu voucher draw cho spin prize:', couponData.couponCode)
  } catch (error) {
    console.error('Lỗi khi lưu voucher draw cho spin prize:', error)
  }
}

const checkUserPlayedGane = async (userId: number, campaignId: number, gameId: number) => {
  const existingHistory = await spinHistoryRepo()
    .createQueryBuilder('spin_history')
    .where('spin_history.userId = :userId', { userId: userId })
    .andWhere('spin_history.campaignId = :campaignId', { campaignId })
    .andWhere('spin_history.gameId = :gameId', { gameId })
    .getCount()

  return existingHistory > 0
}

// Module xử lý Game G1
interface G1GameResult {
  status: string
  message: string
  play_turns: number
}

const processG1Game = async (
  user: any,
  game: any,
  userSpin: any,
  campaignId: number,
  gameId: number,
  prizes: any[],
  context?: string
): Promise<G1GameResult> => {
  let couponData = null
  let message = `Chúc bạn may mắn lần sau!`

  // Kiểm tra xem user đã chơi lần nào chưa
  const existingHistory = await checkUserPlayedGane(user.id, campaignId, gameId)

  let prize: any
  if (!existingHistory) {
    // Lần đầu chơi - bắt buộc trúng voucher100
    const voucher100Prizes = await luckyWheelService.getActivePrizesVoucher100(campaignId, gameId, context)

    if (voucher100Prizes.length > 0) {
      // OPTIMIZATION: Batch query cho voucher100 prizes
      const voucher100PrizeIds = voucher100Prizes.map(p => p.id)
      const voucher100WonCountMap = new Map<number, number>()

      if (voucher100PrizeIds.length > 0) {
        const wonCounts = await spinHistoryRepo()
          .createQueryBuilder('spin_history')
          .select('spin_history.prizeId', 'prizeId')
          .addSelect('COUNT(*)', 'count')
          .where('spin_history.prizeId IN (:...prizeIds)', { prizeIds: voucher100PrizeIds })
          .groupBy('spin_history.prizeId')
          .getRawMany()

        wonCounts.forEach(item => {
          voucher100WonCountMap.set(parseInt(item.prizeId), parseInt(item.count))
        })
      }

      const availableVoucher100 = voucher100Prizes
        .map(v100Prize => {
          const wonCount = voucher100WonCountMap.get(v100Prize.id) || 0
          const remainingQuantity = v100Prize.quantity - wonCount
          return {
            ...v100Prize,
            remainingQuantity
          }
        })
        .filter(p => p.remainingQuantity > 0)

      if (availableVoucher100.length > 0) {
        // Random chọn 1 voucher100 từ danh sách còn số lượng
        const randomIndex = Math.floor(Math.random() * availableVoucher100.length)
        prize = availableVoucher100[randomIndex]
      } else {
        // Nếu voucher100 hết số lượng, dùng logic random như cũ
        prize = randomPrizeWithQuantityCheck(prizes)
      }
    } else {
      // Nếu không có voucher100, dùng logic random như cũ
      prize = randomPrizeWithQuantityCheck(prizes)
    }
  } else {
    // Từ lần thứ 2 trở đi - dùng logic random như cũ
    prize = randomPrizeWithQuantityCheck(prizes)
  }

  if (!prize) {
    throw new NotFoundError('Không có phần thưởng nào', '', 1)
  }

  // Xử lý coupon nếu có bizStorageId
  if (prize.bizStorageId) {
    const couponResult = await processCouponFromBizfly(user, prize, prizes, game, campaignId, gameId)

    if (couponResult.success && couponResult.couponData) {
      couponData = couponResult.couponData
      message = couponResult.message

      // Nếu prize có type 'spin', lưu vào VoucherDraw
      if (prize.type === 'spin') {
        await saveVoucherDrawForSpinPrize(user.id, campaignId, gameId, couponResult.couponData)
      }
    } else {
      if (couponResult.replacementPrize) {
        prize = couponResult.replacementPrize
      }
      message = couponResult.message
    }
  }

  // Lưu vào SpinHistory
  await luckyWheelService.saveSpinHistory(user.id, prize.id || 0, campaignId, gameId, couponData)

  // Trừ lượt chơi (ưu tiên trừ inviteCount trước)
  deductPlayTurn(userSpin)

  // Cộng totalSpinToday
  userSpin.totalSpinToday += 1

  userSpin.updatedAt = getNowGMT7()
  await userSpinRepo().save(userSpin)

  return {
    status: 'success',
    message,
    play_turns: getTotalPlayTurns(userSpin)
  }
}

// Module xử lý Game G2
interface G2GameResult {
  status: string
  message: string
  boxes: any[]
  play_turns: number
}

// Helper function to random select a prize from list based on winrate
const selectPrizeByWinrate = (prizes: any[]): any | null => {
  if (prizes.length === 0) return null
  
  const total = prizes.reduce((sum, p) => sum + (p.winrate || 0), 0)
  
  if (total === 0) {
    // Nếu không có winrate, chọn random đều
    const randomIndex = Math.floor(Math.random() * prizes.length)
    return prizes[randomIndex]
  }
  
  let rand = Math.random() * total
  
  for (const prize of prizes) {
    const prizeWinrate = prize.winrate || 0
    if (rand < prizeWinrate) {
      return prize
    }
    rand -= prizeWinrate
  }
  
  // Fallback
  return prizes[prizes.length - 1]
}

const processG2Game = async (
  user: any,
  game: any,
  userSpin: any,
  campaignId: number,
  gameId: number,
  prizes: any[],
  listBoxId: Array<number> = []
): Promise<G2GameResult> => {
  // Cấu hình số box có thể trúng cùng lúc
  const config = {
    MAX_WINNING_BOXES: game.maxWinPrize || 0, // Mặc định random box trúng trong 3 box mở
  }
  console.log('prizes', prizes)

  // Lọc prizes theo type
  const winPrizes = prizes.filter(p => p.type !== 'lose')
  const losePrizes = prizes.filter(p => p.type === 'lose')

  if (winPrizes.length === 0 && losePrizes.length === 0) {
    throw new NotFoundError('Không có phần thưởng nào', '', 1)
  }

  // Tạo danh sách 9 boxes với reward
  const boxes = Array(9).fill(null)
  const sentBoxIds = listBoxId.length > 0 ? listBoxId : []
  console.log('sentBoxIds', [...sentBoxIds])

  // Xác định số box trúng dựa trên MAX_WINNING_BOXES
  let actualWinningBoxCount = 0
  let predefinedWinningPrize = null

  if (config.MAX_WINNING_BOXES === 0) {
    // Random prize giống G1 để xác định có trúng hay không
    const randomizedPrize = randomPrizeWithQuantityCheck(prizes)
    console.log('randomizedPrize', randomizedPrize)
    if (randomizedPrize && ['voucher', 'spin'].includes(randomizedPrize.type)) {
      actualWinningBoxCount = Math.min(1, sentBoxIds.length) // Tối đa 1 box trúng khi random
    }
    predefinedWinningPrize = randomizedPrize // Lưu prize đã random để dùng cho winning box
  } else {
    // Bắt buộc có số box trúng theo MAX_WINNING_BOXES
    actualWinningBoxCount = Math.min(config.MAX_WINNING_BOXES, sentBoxIds.length)
  }
  console.log('actualWinningBoxCount', actualWinningBoxCount)

  // Chọn random winning boxes từ sentBoxIds
  const winningBoxes = []
  if (actualWinningBoxCount > 0) {
    const remainingSentBoxes = [...sentBoxIds]
    for (let i = 0; i < actualWinningBoxCount; i++) {
      const randomIndex = Math.floor(Math.random() * remainingSentBoxes.length)
      winningBoxes.push(remainingSentBoxes.splice(randomIndex, 1)[0])
    }
    console.log('winningBoxes', [...winningBoxes])
  }

  // Lưu trữ các prize đã sử dụng để tránh lặp lại
  const usedPrizeIds = new Set()
  console.log('sentBoxIds', sentBoxIds)

  // Fill các box được gửi lên
  for (const boxId of sentBoxIds) {
    const boxIndex = boxId - 1
    if (winningBoxes.includes(boxId)) {
      // Box trúng - sử dụng prize đã được định trước hoặc random từ winPrizes
      let prize: any
      console.log('predefinedWinningPrize', predefinedWinningPrize)
      if (predefinedWinningPrize && winningBoxes[0] === boxId) {
        // Sử dụng prize đã random từ MAX_WINNING_BOXES = 0 cho box trúng đầu tiên
        prize = predefinedWinningPrize
      } else {
        // Cho các trường hợp khác, random từ winPrizes còn số lượng theo winrate
        const availableWinPrizes = winPrizes.filter(p =>
          !usedPrizeIds.has(p.id) && p.remainingQuantity > 0
        )
        const prizeToUse = availableWinPrizes.length > 0 ? availableWinPrizes : winPrizes

        // Sử dụng hàm random theo winrate thay vì random đều
        prize = selectPrizeByWinrate(prizeToUse)
      }

      boxes[boxIndex] = {
        id: boxId,
        reward: {
          reward_id: prize.id,
          name: prize.name,
          image: prize.imageSlug ? prize.imageSlug : (prize.image ? prize.image.split('/')[prize.image.split('/').length - 1] : '')
        },
        prizeData: prize // Lưu thông tin prize để xử lý coupon
      }
      usedPrizeIds.add(prize.id)
    } else {
      // Box không trúng trong danh sách gửi lên - dùng lose prizes
      if (losePrizes.length > 0) {
        const availableLosePrizes = losePrizes.filter(p => !usedPrizeIds.has(p.id))
        const prizeToUse = availableLosePrizes.length > 0 ? availableLosePrizes : losePrizes
        const prizeIndex = Math.floor(Math.random() * prizeToUse.length)
        const prize = prizeToUse[prizeIndex]

        boxes[boxIndex] = {
          id: boxId,
          reward: {
            reward_id: prize.id,
            name: prize.name,
            image: prize.imageSlug ? prize.imageSlug : (prize.image ? prize.image.split('/')[prize.image.split('/').length - 1] : '')
          }
        }
        usedPrizeIds.add(prize.id)
      } else {
        // Nếu không có lose prizes, dùng mặc định
        boxes[boxIndex] = {
          id: boxId,
          reward: {
            reward_id: 0,
            name: 'Chúc bạn may mắn lần sau',
            image: ''
          }
        }
      }
    }
  }

  // Fill các box còn lại (không được gửi lên) bằng tất cả prizes (win + lose) trừ đã dùng
  const allPrizes = [...winPrizes, ...losePrizes]
  const availablePrizesForRemaining = allPrizes.filter(p => !usedPrizeIds.has(p.id))
  const prizesToUseForRemaining = availablePrizesForRemaining.length > 0 ? availablePrizesForRemaining : allPrizes

  // Shuffle prizes để random thứ tự
  const shufflePrizes = (array: typeof allPrizes) => {
    const shuffled = [...array]
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
    }
    return shuffled
  }

  const shuffledPrizes = shufflePrizes(prizesToUseForRemaining)
  let remainingPrizeIndex = 0

  for (let i = 1; i <= 9; i++) {
    if (boxes[i - 1] === null) {
      // Box chưa được fill - lấy prize với thứ tự đã shuffle
      const prize = shuffledPrizes[remainingPrizeIndex % shuffledPrizes.length]

      boxes[i - 1] = {
        id: i,
        reward: {
          reward_id: prize.id,
          name: prize.name,
          image: prize.imageSlug ? prize.imageSlug : (prize.image ? prize.image.split('/')[prize.image.split('/').length - 1] : '')
        }
      }
      remainingPrizeIndex++
    }
  }

  // Xử lý coupon cho từng winning box
  const couponDataList = []
  let message = 'Chúc bạn may mắn lần sau!'
  const status = 'success'


  console.log('winningBoxes', winningBoxes)
  console.log('boxes', boxes)

  if (winningBoxes.length > 0) {
      let isGetPrize = false
    const listErrorBox = []

    for (const winningBoxId of winningBoxes) {
      const winningBox = boxes.find(box => box.id === winningBoxId && box.prizeData)
      if (winningBox && winningBox.prizeData) {
        let winningPrize = winningBox.prizeData
        console.log('winningPrize', winningPrize)
        let couponData = null

        if (['voucher', 'spin'].includes(winningPrize.type) && winningPrize.bizStorageId) {
          const couponResult = await processCouponFromBizfly(user, winningPrize, losePrizes, game, campaignId, gameId)

          if (couponResult.success && couponResult.couponData) {
            couponData = couponResult.couponData
            couponDataList.push(couponData)
            isGetPrize = true

            // Nếu prize có type 'spin', lưu vào VoucherDraw
            if (winningPrize.type === 'spin') {
              await saveVoucherDrawForSpinPrize(user.id, campaignId, gameId, couponResult.couponData)
            }
          } else {
            // Nếu API trả về false, tìm prize có type = 'lose' để thay thế
            listErrorBox.push(winningBoxId)
            const losePrize = losePrizes[0]
            if (losePrize) {
              winningPrize = losePrize
              winningBox.reward.reward_id = losePrize.id
              winningBox.reward.name = losePrize.name
              winningBox.reward.image = losePrize.imageSlug ? losePrize.imageSlug : (losePrize.image ? losePrize.image.split('/')[losePrize.image.split('/').length - 1] : '')
            } else {
              winningBox.reward = {
                reward_id: 0,
                name: 'Chúc bạn may mắn lần sau',
                image: ''
              }
            }
          }
        }

        // Lưu vào SpinHistory cho mỗi box trúng
        await luckyWheelService.saveSpinHistory(user.id, winningPrize.id || 0, campaignId, gameId, couponData)
      }
    }

    let newWinningBoxes = [...winningBoxes]
    if (listErrorBox.length > 0) {
      newWinningBoxes = newWinningBoxes.filter(box => !listErrorBox.includes(box))
    }
    const boxNumbers = newWinningBoxes.join(', ')
    if (isGetPrize == true) {
      message = `Bạn đã mở hộp quà số ${boxNumbers} và nhận được voucher!`
    } else {
      message = `Bạn đã mở hộp quà số ${boxNumbers} nhưng đã hết voucher này!`
    }
  }

  // Lưu lịch sử chơi trường hợp không trúng
  console.log('predefinedWinningPrize', predefinedWinningPrize)
  if (predefinedWinningPrize && predefinedWinningPrize.type == 'lose') {
    await luckyWheelService.saveSpinHistory(user.id, predefinedWinningPrize.id || 0, campaignId, gameId, null)
  }

  // Trừ lượt chơi (ưu tiên trừ inviteCount trước)
  deductPlayTurn(userSpin)

  // Cộng totalSpinToday khi claimVoucher thành công
  userSpin.totalSpinToday += 1

  // Lưu các box đã gửi lên vào openedBoxes
  const currentOpenedBoxes = userSpin.openedBoxes ? JSON.parse(userSpin.openedBoxes) : []
  const newOpenedBoxes = [...new Set([...currentOpenedBoxes, ...sentBoxIds])] // Loại bỏ duplicate
  userSpin.openedBoxes = JSON.stringify(newOpenedBoxes)

  userSpin.updatedAt = getNowGMT7()
  await userSpinRepo().save(userSpin)

  // Loại bỏ prizeData khỏi response
  const responseBoxes = boxes.map(box => ({
    id: box.id,
    reward: box.reward
  }))

  return {
    status,
    message,
    boxes: responseBoxes,
    play_turns: getTotalPlayTurns(userSpin)
  }
}

// Module xử lý khi hết lượt chơi
interface NoTurnsResult {
  status: string
  message: string
  received_rewards?: any[]
  boxes?: any[]
  opened_boxes?: number[]
  share_remaining: number
}

const processNoTurns = async (
  user: any,
  game: any,
  userSpin: any,
  campaignId: number,
  gameId: number,
  gameIdSlug: string,
  context?: string
): Promise<NoTurnsResult> => {
  // Kiểm tra share_remaining
  const now = getNowGMT7()
  const shareRemaining = userSpin.lastRequestFacebook && isSameDay(userSpin.lastRequestFacebook, now) ? 0 : 1

  if (gameIdSlug === 'g1') {
    // Hết lượt chơi G1 - lấy danh sách phần thưởng đã nhận
    const receivedRewards = await spinHistoryRepo()
      .createQueryBuilder('spin_histories')
      .leftJoinAndSelect('spin_histories.prize', 'prize')
      .where('spin_histories.userId = :userId', { userId: user.id })
      .andWhere('spin_histories.campaignId = :campaignId', { campaignId })
      .andWhere('spin_histories.gameId = :gameId', { gameId })
      .getMany()

    const rewards = receivedRewards.map(history => ({
      id: history.prize.id,
      name: history.voucherName || history.prize.name
    }))

    return {
      status: 'no_turns',
      message: 'Bạn đã hết lượt chơi hôm nay',
      received_rewards: rewards,
      share_remaining: shareRemaining
    }
  }

  if (gameIdSlug === 'g2') {
    // Hết lượt chơi G2 - trả về tất cả boxes
    const prizes = await getPrizesWithRemainingQuantity(campaignId, game.id, context)
    const winPrizes = prizes.filter(p => p.type !== 'lose')
    const losePrizes = prizes.filter(p => p.type === 'lose')
    const allPrizes = [...winPrizes, ...losePrizes]

    // Shuffle prizes để random thứ tự
    const shufflePrizes = (array: typeof allPrizes) => {
      const shuffled = [...array]
      for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]]
      }
      return shuffled
    }

    const shuffledAllPrizes = shufflePrizes(allPrizes)

    const boxes = []
    for (let i = 1; i <= 9; i++) {
      const prizeIndex = (i - 1) % shuffledAllPrizes.length
      const prize = shuffledAllPrizes[prizeIndex]
      boxes.push({
        id: i,
        reward: {
          reward_id: prize.id,
          name: prize.name,
          image: prize.image
        }
      })
    }

    // Lấy thông tin các box đã mở và phần quà đã nhận
    const openedBoxes = userSpin.openedBoxes ? JSON.parse(userSpin.openedBoxes) : []
    const receivedRewards = await spinHistoryRepo()
      .createQueryBuilder('spin_histories')
      .leftJoinAndSelect('spin_histories.prize', 'prize')
      .where('spin_histories.userId = :userId', { userId: user.id })
      .andWhere('spin_histories.campaignId = :campaignId', { campaignId })
      .andWhere('spin_histories.gameId = :gameId', { gameId })
      .getMany()

    const rewards = receivedRewards.map(history => ({
      id: history.prize.id,
      name: history.voucherName || history.prize.name,
      image: history.prize.imageSlug || history.prize.image,
      spin_time: history.spinTime
    }))

    return {
      status: 'no_turns',
      message: 'Bạn đã hết lượt chơi hôm nay',
      boxes,
      opened_boxes: openedBoxes,
      received_rewards: rewards,
      share_remaining: shareRemaining
    }
  }

  // Default fallback
  return {
    status: 'no_turns',
    message: 'Bạn đã hết lượt chơi hôm nay',
    share_remaining: shareRemaining
  }
}

/**
 * 1. GET /api/game/init
 * Initialize game, preload user data and game UI
 */
export const initGame = async (req: AuthorizedUserRequest, gameIdSlug: string) => {
  const { user } = req.authorizedUser
  const gameId = convertGameIdSlugToNumber(gameIdSlug)

  if (!gameId) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  // Lấy thông tin game để có campaignId
  const game = await gameRepo().findOne({
    where: { id: gameId }
  })

  if (!game) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  const campaignId = game.campaignId

  // Lấy thông tin UserSpin
  let userSpin = await userSpinRepo().findOne({
    where: { userId: user.id, campaignId, gameId }
  })

  // Nếu chưa có UserSpin, tạo mới với số lượt mặc định
  if (!userSpin) {
    const now = getNowGMT7()
    userSpin = new UserSpin()
    userSpin.userId = user.id
    userSpin.campaignId = campaignId
    userSpin.gameId = gameId
    userSpin.createdAt = now
    userSpin.updatedAt = now
    userSpin.lastRequest = now
    userSpin.spinCounts = getDefaultSpinCountsByGameId(gameId) // Số lượt chơi mặc định theo game
    await userSpinRepo().save(userSpin)
  } else {
    const now = getNowGMT7()
    let needSave = false

    // Kiểm tra nếu lastRequest là ngày hôm trước thì reset dữ liệu ngày mới
    if (!userSpin.lastRequest || !isSameDay(userSpin.lastRequest, now)) {
      // Reset totalSpinToday và openedBoxes khi sang ngày mới
      userSpin.totalSpinToday = 0
      userSpin.openedBoxes = null
      userSpin.lastRequest = now
      needSave = true
    }

    if(userSpin.spinCounts == 0){
        // Kiểm tra nếu lastRequest là ngày hôm trước thì cộng thêm lượt chơi cho ngày mới
        if (!userSpin.lastRequest || !isSameDay(userSpin.lastRequest, now)) {
          userSpin.spinCounts += getDefaultSpinCountsByGameId(gameId)
          needSave = true
        }
    }

    const defaultSpinCounts = getDefaultSpinCountsByGameId(gameId)
    if(userSpin.spinCounts < defaultSpinCounts){
      userSpin.spinCounts = defaultSpinCounts
      needSave = true
    }

    if (needSave) {
      userSpin.updatedAt = now
      await userSpinRepo().save(userSpin)
    }
  }

  const existingHistory = await checkUserPlayedGane(user.id, campaignId, gameId);

  // Chuẩn bị response data
  const responseData: any = {
    status: 'success',
    user: {
      id: user.id,
      name: user.name
    },
    play_turns: getTotalPlayTurns(userSpin),
    is_share_turn: getShareTurnByGameId(gameId, userSpin.totalSpinToday),
    first_play: !existingHistory
  }

  // Nếu là game G2, thêm thông tin về các box đã mở và phần quà
  if (gameIdSlug === 'g2') {
    // Lấy danh sách các box đã mở
    const openedBoxes = userSpin.openedBoxes ? JSON.parse(userSpin.openedBoxes) : []

    // Lấy thông tin phần quà đã nhận từ SpinHistory
    const receivedRewards = await spinHistoryRepo()
      .createQueryBuilder('spin_histories')
      .leftJoinAndSelect('spin_histories.prize', 'prize')
      .where('spin_histories.userId = :userId', { userId: user.id })
      .andWhere('spin_histories.campaignId = :campaignId', { campaignId })
      .andWhere('spin_histories.gameId = :gameId', { gameId })
      .getMany()

    const rewards = receivedRewards.map(history => ({
      id: history.prize.id,
      name: history.voucherName || history.prize.name,
      image: history.prize.imageSlug || history.prize.image,
      spin_time: history.spinTime
    }))

    responseData.opened_boxes = openedBoxes
    responseData.received_rewards = rewards
  }

  return responseData
}

/**
 * 2. GET /api/game/play
 * Start the game and get vouchers that will fall
 */
export const playGame = async (req: AuthorizedUserRequest, gameIdSlug: string) => {
  const { user } = req.authorizedUser
  const gameId = convertGameIdSlugToNumber(gameIdSlug)

  if (!gameId) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  // Lấy thông tin game để có campaignId
  const game = await gameRepo().findOne({
    where: { id: gameId }
  })

  if (!game) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  const campaignId = game.campaignId

  // Lấy thông tin UserSpin
  const userSpin = await userSpinRepo().findOne({
    where: { userId: user.id, campaignId, gameId }
  })

  if (!userSpin) {
    throw new NotFoundError('Vui lòng khởi tạo game trước', '', 1)
  }

  // Lấy danh sách phần quà (vouchers)
  const prizes = await prizeRepo().find({
    where: { campaignId, gameId, active: 1 },
    order: { displayOrder: 'ASC' }
  })

  const vouchers = prizes.map(prize => ({
    id: prize.bizStorageId || prize.id,
    value: prize.name,
    image: prize.image
  }))

  return {
    status: 'success',
    play_turns: getTotalPlayTurns(userSpin),
    vouchers
  }
}

/**
 * 3. POST /api/game/claim
 * Claim a voucher by tapping it
 */
export const claimVoucher = async (req: AuthorizedUserRequest, gameIdSlug: string, listBoxId: Array<number> = [], context?: string) => {
  const { user } = req.authorizedUser
  const gameId = convertGameIdSlugToNumber(gameIdSlug)

  if (!gameId) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  const game = await gameRepo().findOne({
    where: { id: gameId }
  })

  if (!game) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  const campaignId = game.campaignId

  // Lấy thông tin UserSpin
  const userSpin = await userSpinRepo().findOne({
    where: { userId: user.id, campaignId, gameId }
  })

  if (!userSpin) {
    throw new NotFoundError('Vui lòng khởi tạo game trước', '', 1)
  }

  // Nếu còn lượt chơi (tổng của spinCounts + inviteCount)
  if (getTotalPlayTurns(userSpin) > 0) {
    const prizes = await getPrizesWithRemainingQuantity(campaignId, game.id, context)

    if (gameIdSlug === 'g1') {
      // Sử dụng module xử lý Game G1
      return processG1Game(user, game, userSpin, campaignId, gameId, prizes, context)
    }
    else if (gameIdSlug === 'g2') {
      // Sử dụng module xử lý Game G2
      return processG2Game(user, game, userSpin, campaignId, gameId, prizes, listBoxId)
    }
  } else {
    // Sử dụng module xử lý khi hết lượt chơi
    return processNoTurns(user, game, userSpin, campaignId, gameId, gameIdSlug, context)
  }
}

/**
 * 4. POST /api/game/share
 * Share to receive 1 extra play turn (1 time per day)
 */
export const shareForExtraTurn = async (req: AuthorizedUserRequest, gameIdSlug: string, platform: string) => {
  const { user } = req.authorizedUser
  const gameId = convertGameIdSlugToNumber(gameIdSlug)

  if (!gameId) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  // Lấy thông tin game để có campaignId
  const game = await gameRepo().findOne({
    where: { id: gameId }
  })

  if (!game) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  const campaignId = game.campaignId

  // Lấy thông tin UserSpin
  const userSpin = await userSpinRepo().findOne({
    where: { userId: user.id, campaignId, gameId }
  })

  if (!userSpin) {
    throw new NotFoundError('Vui lòng khởi tạo game trước', '', 1)
  }

  const now = getNowGMT7()
   // Kiểm tra đã share facebook hôm nay chưa
  if(platform === 'facebook'){
    if (userSpin.lastRequestFacebook && isSameDay(userSpin.lastRequestFacebook, now)) {
        return {
            status: 'already_shared',
            message: 'Bạn đã chia sẻ và nhận lượt hôm nay rồi'
        }
    }
    // Cộng thêm 1 lượt chơi
    userSpin.spinCounts += 1
    userSpin.lastRequestFacebook = now
    userSpin.updatedAt = now
    await userSpinRepo().save(userSpin)

    return {
        status: 'success',
        message: 'Bạn đã nhận thêm 1 lượt chơi!',
        play_turns: getTotalPlayTurns(userSpin)
      }
  }else{
    return {
        status: 'error',
        message: 'Không hỗ trợ platform này'
    }
  }
}

/**
 * 5. POST /api/game/history
 * Lấy SpinHistory theo user, game_id
 */
export const getSpinHistoryByUserGameCampaign = async (userId: number, gameId: number) => {
  return spinHistoryRepo()
    .createQueryBuilder('spin_histories')
    .leftJoinAndSelect('spin_histories.prize', 'prize')
    .where('spin_histories.userId = :userId', { userId })
    .andWhere('spin_histories.gameId = :gameId', { gameId })
    .orderBy('spin_histories.spinTime', 'DESC')
    .getMany()
}

/**
 * 6. POST /api/game/vocher-storage
 * Lấy SpinHistory theo user chỉ những record có voucherCode và voucherName
 */
// 
export const getSpinHistoryWithVoucherByUserCampaign = async (userId: number) => {
  const query = spinHistoryRepo()
    .createQueryBuilder('spin_histories')
    .leftJoinAndSelect('spin_histories.prize', 'prize')
    .where('spin_histories.userId = :userId', { userId })
    .andWhere('spin_histories.voucherCode IS NOT NULL')
    .andWhere('spin_histories.voucherCode != ""')

  return query.orderBy('spin_histories.spinTime', 'DESC').getMany()
}

/**
 * 7. POST /api/game/invite
 * Tặng quà cho người mời (random từ prize voucher/spin/lose theo tỉ lệ trúng)
 */
export const giveInviteReward = async (
  req: AuthorizedUserRequest, 
  inviterUserId: number, 
  gameId: number
) => {
  const { user: invitedUser } = req.authorizedUser
  
  if (!gameId) {
    throw new NotFoundError('Game ID không hợp lệ', '', 1)
  }

  // Kiểm tra user được mời có tồn tại và khác user mời
  if (invitedUser.id === inviterUserId) {
    throw new NotFoundError('Không thể mời chính mình', '', 1)
  }

  // Lấy thông tin game để có campaignId
  const game = await gameRepo().findOne({
    where: { id: gameId }
  })

  if (!game) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  const campaignId = game.campaignId

  // Kiểm tra xem đã có invite relationship này chưa (cả 2 chiều)
  const existingInvite = await inviteHistoryRepo()
    .createQueryBuilder('invite_history')
    .where(
      '((invite_history.inviterUserId = :inviterUserId AND invite_history.invitedUserId = :invitedUserId) OR ' +
      '(invite_history.inviterUserId = :invitedUserId AND invite_history.invitedUserId = :inviterUserId)) ' +
      'AND invite_history.campaignId = :campaignId AND invite_history.gameId = :gameId',
      {
        inviterUserId: inviterUserId,
        invitedUserId: invitedUser.id,
        campaignId: campaignId,
        gameId: gameId
      }
    )
    .getOne()

  if (existingInvite) {
    const isReverse = existingInvite.inviterUserId === invitedUser.id
    return {
      status: 'already_rewarded',
      message: isReverse 
        ? 'Bạn bè này đã mời bạn trước đó rồi. Không thể mời ngược lại!' 
        : 'Bạn đã được cộng lượt mời này rồi',
      data: {
        inviter_user_id: inviterUserId,
        invited_user_id: invitedUser.id,
        already_rewarded: true,
        is_reverse_invite: isReverse
      }
    }
  }

  const rewardAction = game.invitePrize === 'give_gift' ? 'give_gift' : 'add_spin' 
  // Kiểm tra user mời có tồn tại
  const inviterUser = await userRepo().findOne({
    where: { id: inviterUserId }
  })

  if (!inviterUser) {
    throw new NotFoundError('User mời không tồn tại', '', 1)
  }

  // Lấy thông tin UserSpin của người mời
  let inviterUserSpin = await userSpinRepo().findOne({
    where: { userId: inviterUserId, campaignId, gameId }
  })

  // Nếu chưa có UserSpin, tạo mới
  if (!inviterUserSpin) {
    const now = getNowGMT7()
    inviterUserSpin = new UserSpin()
    inviterUserSpin.userId = inviterUserId
    inviterUserSpin.campaignId = campaignId
    inviterUserSpin.gameId = gameId
    inviterUserSpin.createdAt = now
    inviterUserSpin.lastRequest = now
    inviterUserSpin.spinCounts = getDefaultSpinCountsByGameId(gameId) // Số lượt chơi mặc định theo game
    inviterUserSpin.inviteCount = 0
    await userSpinRepo().save(inviterUserSpin)
  }

  // Lấy thông tin UserSpin của người được mời
  let invitedUserSpin = await userSpinRepo().findOne({
    where: { userId: invitedUser.id, campaignId, gameId }
  })

  // Nếu chưa có UserSpin, tạo mới cho người được mời
  if (!invitedUserSpin) {
    const now = getNowGMT7()
    invitedUserSpin = new UserSpin()
    invitedUserSpin.userId = invitedUser.id
    invitedUserSpin.campaignId = campaignId
    invitedUserSpin.gameId = gameId
    invitedUserSpin.createdAt = now
    invitedUserSpin.lastRequest = now
    invitedUserSpin.spinCounts = getDefaultSpinCountsByGameId(gameId) // Số lượt chơi mặc định theo game
    invitedUserSpin.inviteCount = 0
    await userSpinRepo().save(invitedUserSpin)
  }

  if (rewardAction === 'add_spin') {
    // Cộng lượt chơi cho cả 2 người
    // Người mời
    inviterUserSpin.inviteCount += 1
    inviterUserSpin.updatedAt = getNowGMT7()
    await userSpinRepo().save(inviterUserSpin)

    // Người được mời cũng được cộng lượt
    invitedUserSpin.inviteCount += 1
    invitedUserSpin.updatedAt = getNowGMT7()
    await userSpinRepo().save(invitedUserSpin)

    // Lưu invite history để tránh duplicate
    const inviteHistory = new InviteHistory()
    inviteHistory.inviterUserId = inviterUserId
    inviteHistory.invitedUserId = invitedUser.id
    inviteHistory.campaignId = campaignId
    inviteHistory.gameId = gameId
    inviteHistory.rewardGiven = 1
    inviteHistory.createdAt = getNowGMT7()
    await inviteHistoryRepo().save(inviteHistory)

    return {
      status: 'success',
      message: `Cả 2 bạn đều được cộng thêm 1 lượt chơi nhờ kết bạn!`,
      data: {
        inviter_user_id: inviterUserId,
        invited_user_id: invitedUser.id,
        action: 'add_spin',
        inviter: {
          play_turns: getTotalPlayTurns(inviterUserSpin),
          spin_counts: inviterUserSpin.spinCounts,
          invite_count: inviterUserSpin.inviteCount
        },
        invited: {
          play_turns: getTotalPlayTurns(invitedUserSpin),
          spin_counts: invitedUserSpin.spinCounts,
          invite_count: invitedUserSpin.inviteCount
        }
      }
    }
  }

  // rewardAction === 'give_gift' - Tặng quà cho cả 2 người
  try {
    // Sử dụng hàm có sẵn để lấy prizes với remainingQuantity
    const prizes = await getPrizesWithRemainingQuantity(campaignId, gameId, '')

    if (prizes.length === 0) {
      throw new NotFoundError('Không có phần thưởng nào trong game', '', 1)
    }

    // Random prize cho người mời
    const inviterSelectedPrize = randomPrizeWithQuantityCheck(prizes)
    if (!inviterSelectedPrize) {
      throw new NotFoundError('Không có phần thưởng hợp lệ cho người mời', '', 1)
    }

    // Random prize cho người được mời
    const invitedSelectedPrize = randomPrizeWithQuantityCheck(prizes)
    if (!invitedSelectedPrize) {
      throw new NotFoundError('Không có phần thưởng hợp lệ cho người được mời', '', 1)
    }

    // Xử lý prize cho người mời
    let inviterCouponData = null
    let inviterMessage = `Bạn đã nhận được ${inviterSelectedPrize.name}!`

    if (inviterSelectedPrize.bizStorageId && ['voucher', 'spin'].includes(inviterSelectedPrize.type)) {
      const couponResult = await processCouponFromBizfly(
        inviterUser, 
        inviterSelectedPrize, 
        prizes, 
        game, 
        campaignId, 
        gameId
      )

      if (couponResult.success && couponResult.couponData) {
        inviterCouponData = couponResult.couponData
        inviterMessage = couponResult.message

        // Nếu prize có type 'spin', lưu vào VoucherDraw
        if (inviterSelectedPrize.type === 'spin') {
          await saveVoucherDrawForSpinPrize(inviterUserId, campaignId, gameId, couponResult.couponData)
        }
      } else {
        if (couponResult.replacementPrize) {
          inviterSelectedPrize.id = couponResult.replacementPrize.id
          inviterSelectedPrize.name = couponResult.replacementPrize.name
          inviterSelectedPrize.type = couponResult.replacementPrize.type
        }
        inviterMessage = couponResult.message || 'Chúc bạn may mắn lần sau!'
      }
    }

    // Xử lý prize cho người được mời
    let invitedCouponData = null
    let invitedMessage = `Bạn đã nhận được ${invitedSelectedPrize.name}!`

    if (invitedSelectedPrize.bizStorageId && ['voucher', 'spin'].includes(invitedSelectedPrize.type)) {
      const couponResult = await processCouponFromBizfly(
        invitedUser, 
        invitedSelectedPrize, 
        prizes, 
        game, 
        campaignId, 
        gameId
      )

      if (couponResult.success && couponResult.couponData) {
        invitedCouponData = couponResult.couponData
        invitedMessage = couponResult.message
         // Nếu prize có type 'spin', lưu vào VoucherDraw
        if (invitedSelectedPrize.type === 'spin') {
          await saveVoucherDrawForSpinPrize(invitedUser.id, campaignId, gameId, couponResult.couponData)
        }
      } else {
        if (couponResult.replacementPrize) {
          invitedSelectedPrize.id = couponResult.replacementPrize.id
          invitedSelectedPrize.name = couponResult.replacementPrize.name
          invitedSelectedPrize.type = couponResult.replacementPrize.type
        }
        invitedMessage = couponResult.message || 'Chúc bạn may mắn lần sau!'
      }
    }

    // Lưu vào SpinHistory cho cả 2 người
    await luckyWheelService.saveSpinHistory(inviterUserId, inviterSelectedPrize.id, campaignId, gameId, inviterCouponData)
    await luckyWheelService.saveSpinHistory(invitedUser.id, invitedSelectedPrize.id, campaignId, gameId, invitedCouponData)

    // Lưu invite history để tránh duplicate
    const inviteHistory = new InviteHistory()
    inviteHistory.inviterUserId = inviterUserId
    inviteHistory.invitedUserId = invitedUser.id
    inviteHistory.campaignId = campaignId
    inviteHistory.gameId = gameId
    inviteHistory.rewardGiven = 1
    inviteHistory.createdAt = getNowGMT7()
    await inviteHistoryRepo().save(inviteHistory)

    // Tạo prize info cho response
    const inviterPrizeInfo = {
      id: inviterSelectedPrize.id,
      name: inviterSelectedPrize.name,
      type: inviterSelectedPrize.type,
      voucher_code: inviterCouponData?.couponCode || null,
      voucher_name: inviterCouponData?.couponName || null,
      voucher_link: inviterCouponData?.qrCodeLink || null,
      voucher_expiry: inviterCouponData?.end_date || null
    }

    const invitedPrizeInfo = {
      id: invitedSelectedPrize.id,
      name: invitedSelectedPrize.name,
      type: invitedSelectedPrize.type,
      voucher_code: invitedCouponData?.couponCode || null,
      voucher_name: invitedCouponData?.couponName || null,
      voucher_link: invitedCouponData?.qrCodeLink || null,
      voucher_expiry: invitedCouponData?.end_date || null
    }

    return {
      status: 'success',
      message: 'Cả 2 bạn đều nhận được quà tặng!',
      data: {
        inviter_user_id: inviterUserId,
        invited_user_id: invitedUser.id,
        action: 'give_gift',
        inviter: {
          prize: inviterPrizeInfo,
          message: inviterMessage,
          play_turns: getTotalPlayTurns(inviterUserSpin),
          spin_counts: inviterUserSpin.spinCounts,
          invite_count: inviterUserSpin.inviteCount
        },
        invited: {
          prize: invitedPrizeInfo,
          message: invitedMessage,
          play_turns: getTotalPlayTurns(invitedUserSpin),
          spin_counts: invitedUserSpin.spinCounts,
          invite_count: invitedUserSpin.inviteCount
        }
      }
    }
  } catch (error) {
    throw new NotFoundError(`Lỗi khi tặng phần thưởng: ${error.message}`, '', 1)
  }
}

export const drawGift = async (campaignId: number, prizeId: number, createdBy: number) => {
    const now = getNowGMT7();
    console.log(`Bắt đầu quay thưởng cho campaign ${campaignId}, type_win ${prizeId}`);

    // Lấy danh sách user có reward code và chưa được quay bao giờ trong campaign này
    // Điều kiện: chưa có record nào trong user_gift_award với campaign này (bất kể typeWin nào)
    const eligibleUserGiftsQuery = `
      SELECT 
        vd.id,
        vd.phone,
        vd.voucher_code,
        vd.source,
        vd.user_id
      FROM voucher_draw vd
      WHERE vd.campaign_id = ?
        AND vd.voucher_code != ''
        AND vd.phone IS NOT NULL
        AND vd.phone NOT IN (
          SELECT DISTINCT award.phone 
          FROM user_gift_award award
          INNER JOIN voucher_draw vd2 ON vd2.id = award.voucher_draw_id
          WHERE vd2.campaign_id = ?
        )
      ORDER BY RAND()
      LIMIT 1
    `;

    const params = [campaignId, campaignId];
    const winnerResult = await AppDataSource.manager.query(eligibleUserGiftsQuery, params);

    if (winnerResult.length === 0) {
      throw new Error('Không có user nào đủ điều kiện quay thưởng (tất cả user đã được quay rồi)');
    }

    const winner = winnerResult[0];
    console.log('winner', winner);

    // Lấy thông tin khách hàng từ Bizfly
    let customerName = ''
    let customerEmail = ''
    if (winner.phone && createdBy) {
      try {
        const customerInfo = await luckyWheelService.getInfoCustomerFromBizfly(createdBy, winner.phone)
        console.log('customerInfo', customerInfo)
        if (customerInfo && customerInfo.data && customerInfo.data && customerInfo.data.length > 0) {
          const customerData = customerInfo.data[0]
          const nameField = customerData.find(field => field.key === 'name')
          const emailField = customerData.find(field => field.key === 'emails')

          if (nameField && nameField.value) {
            customerName = nameField.value.value
          }
          if (emailField && emailField.value && emailField.value.length > 0) {
            customerEmail = emailField.value[0].value
          }
        }
      } catch (error) {
        console.error('Không thể lấy thông tin khách hàng từ Bizfly:', error)
        // Bỏ qua lỗi và tiếp tục mà không có thông tin này
      }
    }

    // Cập nhật tất cả các record cũ trong bảng award với campaignId và typeWin thành is_win = 0
    await userGiftAwardRepo
      .createQueryBuilder()
      .update(UserGiftAward)
      .set({ isWin: 0, updatedAt: now })
      .where('prizeId = :prizeId', { prizeId })
      .andWhere('campaign_id = :campaignId', { campaignId })
      .execute();

    console.log(`Đã cập nhật tất cả record cũ với campaign ${campaignId} và type_win ${prizeId} thành is_win = 0`);

    // Chỉ tạo record cho winner
    const winnerAward = userGiftAwardRepo.create({
      voucherDrawId: winner.id,
      userId: winner.user_id ? winner.user_id : 0,
      isWin: 1,
      campaignId: campaignId,
      prizeId: prizeId,
      createdBy: createdBy,
      phone: winner.phone,
      createdAt: now,
      updatedAt: now
    });

    await userGiftAwardRepo.save(winnerAward);

    return {
      winner: {
        voucherDrawId: winner.id,
        rewardCode: winner.voucher_code,
        phone: winner.phone,
        source: winner.source,
        name: customerName,
        email: customerEmail
      },
      campaignId,
      prizeId,
      createdBy,
      message: `Quay thưởng thành công! User ${winner.phone} đã trúng thưởng.`
    };
  }

// Export các helper functions để sử dụng trong guest API
export { getPrizesWithRemainingQuantity, randomPrizeWithQuantityCheck, selectPrizeByWinrate }

/**
 * API merge guest data khi user đăng nhập
 */
export const mergeGuestDataToUser = async (
  userId: number,
  guestSessionId: string
) => {
  // Lấy thông tin guest session
  const guestSession = await guestGameSessionRepo().findOne({
    where: { guestSessionId, isMerged: false }
  })

  if (!guestSession) {
    throw new NotFoundError('Guest session không tồn tại hoặc đã được merge', '', 1)
  }

  // Kiểm tra xem user này đã có guest session nào được merge trong cùng gameId chưa
  const existingMergedSession = await guestGameSessionRepo().findOne({
    where: { 
      mergedUserId: userId, 
      gameId: guestSession.gameId, 
      isMerged: true 
    }
  })

  if (existingMergedSession) {
    throw new NotFoundError('User đã được merge dữ liệu guest trong game này rồi', '', 1)
  }

  // Lấy thông tin user
  const user = await userRepo().findOne({
    where: { id: userId }
  })

  if (!user) {
    throw new NotFoundError('User không tồn tại', '', 1)
  }

  // Lấy thông tin game
  const game = await gameRepo().findOne({
    where: { id: guestSession.gameId }
  })

  if (!game) {
    throw new NotFoundError('Game không tồn tại', '', 1)
  }

  const processedCoupons = []
  const errors = []

      // Kiểm tra xem user đã chơi lần nào chưa
  const existingHistory = await checkUserPlayedGane(user.id, game.campaignId, game.id)
  if(existingHistory) {
    throw new NotFoundError('User đã từng chơi game rồi', '', 1)
  }

  // Xử lý từng prize đã thắng
  for (const prizeWon of guestSession.prizesWon || []) {
    try {
      if (prizeWon.bizStorageId && ['voucher', 'spin'].includes(prizeWon.type)) {
        // Gọi API lấy coupon thật từ Bizfly
        const couponResponse = await luckyWheelService.getCouponFromBizfly(
          userId,
          prizeWon.bizStorageId,
          user.tokyoId,
          user.bizId,
          game.name,
          guestSession.campaignId,
          guestSession.gameId,
          guestSession.gameId == 27 ? 4 : 5
        )

        if (couponResponse && couponResponse.data) {
          const couponData = {
            couponId: couponResponse.data.coupon_id,
            couponCode: couponResponse.data.code,
            couponName: couponResponse.data.name,
            qrCodeLink: couponResponse.data.link_scan_qr_code,
            end_date: couponResponse.data.end_date
          }

          // Lưu vào SpinHistory
          await luckyWheelService.saveSpinHistory(
            userId,
            prizeWon.id,
            guestSession.campaignId,
            guestSession.gameId,
            couponData
          )

          // Nếu prize có type 'spin', lưu vào VoucherDraw
          if (prizeWon.type === 'spin') {
            await saveVoucherDrawForSpinPrize(
              userId,
              guestSession.campaignId,
              guestSession.gameId,
              couponData
            )
          }

          processedCoupons.push({
            prize: prizeWon,
            coupon: couponData,
            success: true
          })
        } else {
          // API trả về false, lưu với coupon null
          await luckyWheelService.saveSpinHistory(
            userId,
            prizeWon.id,
            guestSession.campaignId,
            guestSession.gameId,
            null
          )

          errors.push({
            prize: prizeWon,
            error: 'Không thể lấy coupon từ Bizfly'
          })
        }
      } else {
        // Prize không có bizStorageId hoặc là lose prize
        await luckyWheelService.saveSpinHistory(
          userId,
          prizeWon.id,
          guestSession.campaignId,
          guestSession.gameId,
          null
        )

        processedCoupons.push({
          prize: prizeWon,
          coupon: null,
          success: true
        })
      }
    } catch (error) {
      console.error('Lỗi khi xử lý prize:', prizeWon, error)
      errors.push({
        prize: prizeWon,
        error: error.message
      })
    }
  }

  // Đánh dấu guest session đã được merge
  guestSession.isMerged = true
  guestSession.mergedUserId = userId
  guestSession.updatedAt = getNowGMT7()
  await guestGameSessionRepo().save(guestSession)

  return {
    status: 'success',
    message: 'Đã merge dữ liệu guest thành công',
    data: {
      guest_session_id: guestSessionId,
      user_id: userId,
      processed_coupons: processedCoupons,
      errors: errors
    }
  }
}