-- Game3 Migration Script (Optimized)
-- Create tables for Tokyo Life Game G3

-- 1. Create locations table (reusable for future games)
DROP TABLE IF EXISTS `locations`;
CREATE TABLE `locations` (
  `id` int AUTO_INCREMENT PRIMARY KEY,
  `name` varchar(255) NOT NULL,
  `rules` text NULL,
  `active` int DEFAULT 1,
  `sort_order` int DEFAULT 0,
  `type` varchar(50) DEFAULT 'game3',
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6)
);

-- 2. Create location_requirements table
DROP TABLE IF EXISTS `location_requirements`;
CREATE TABLE `location_requirements` (
  `id` int AUTO_INCREMENT PRIMARY KEY,
  `location_id` int NOT NULL,
  `item_type` varchar(50) NOT NULL,
  `item_name` varchar(255) NOT NULL,
  `required_quantity` int NOT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  FOREIGN KEY (`location_id`) REFERENCES `locations`(`id`) ON DELETE CASCADE
);

-- 3. Add new fields to existing user_spins table (reuse instead of creating new table)
ALTER TABLE `user_spins`
ADD COLUMN `collected_letters` json NULL,
ADD COLUMN `last_share_date` datetime NULL;

-- 4. Create game3_user_items table (keep this as it's specific to game3)
DROP TABLE IF EXISTS `game3_user_items`;
CREATE TABLE `game3_user_items` (
  `id` int AUTO_INCREMENT PRIMARY KEY,
  `user_id` int NOT NULL,
  `campaign_id` int NOT NULL,
  `game_id` int NOT NULL,
  `location_id` int NOT NULL,
  `item_type` varchar(50) NOT NULL,
  `quantity` int DEFAULT 0,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  UNIQUE KEY `unique_user_location_item` (`user_id`, `campaign_id`, `game_id`, `location_id`, `item_type`),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`location_id`) REFERENCES `locations`(`id`) ON DELETE CASCADE
);

-- 5. Create game3_play_sessions table (keep this for session management)
DROP TABLE IF EXISTS `game3_play_sessions`;
CREATE TABLE `game3_play_sessions` (
  `id` int AUTO_INCREMENT PRIMARY KEY,
  `code` varchar(255) NOT NULL UNIQUE,
  `user_id` int NOT NULL,
  `campaign_id` int NOT NULL,
  `game_id` int NOT NULL,
  `location_id` int NOT NULL,
  `status` enum('active', 'completed', 'expired') DEFAULT 'active',
  `spawn_items` json NULL,
  `spawn_letters` json NULL,
  `expires_at` datetime NOT NULL,
  `created_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6),
  `updated_at` datetime(6) NOT NULL DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6),
  FOREIGN KEY (`user_id`) REFERENCES `users`(`id`) ON DELETE CASCADE,
  FOREIGN KEY (`location_id`) REFERENCES `locations`(`id`) ON DELETE CASCADE
);

-- Note: Removed game3_location_rewards table - will use existing prizes and spin_histories tables instead

-- Insert sample data for locations
INSERT INTO `locations` (`name`, `rules`, `active`, `sort_order`, `type`) VALUES
('Tuyên Quang', '<h3>Thể lệ chương trình tại Tuyên Quang</h3><p>Thu thập đủ 15 áo, 10 quần, 25 balo để nhận phần thưởng.</p>', 1, 1, 'game3'),
('Điện Biên', '<h3>Thể lệ chương trình tại Điện Biên</h3><p>Thu thập đủ 20 áo, 15 quần, 30 balo để nhận phần thưởng.</p>', 1, 2, 'game3'),
('Hà Giang', '<h3>Thể lệ chương trình tại Hà Giang</h3><p>Thu thập đủ 25 áo, 20 quần, 35 balo để nhận phần thưởng.</p>', 1, 3, 'game3'),
('Sapa', '<h3>Thể lệ chương trình tại Sapa</h3><p>Thu thập đủ 30 áo, 25 quần, 40 balo để nhận phần thưởng.</p>', 1, 4, 'game3');

-- Insert sample requirements for each location
-- Tuyên Quang (id: 1)
INSERT INTO `location_requirements` (`location_id`, `item_type`, `item_name`, `required_quantity`) VALUES
(1, 'ao', 'Áo', 15),
(1, 'quan', 'Quần', 10),
(1, 'balo', 'Balo', 25),
(1, 'letter', 'T', 1),
(1, 'letter', 'O', 1),
(1, 'letter', 'K', 1);

-- Điện Biên (id: 2)
INSERT INTO `location_requirements` (`location_id`, `item_type`, `item_name`, `required_quantity`) VALUES
(2, 'ao', 'Áo', 20),
(2, 'quan', 'Quần', 15),
(2, 'balo', 'Balo', 30),
(2, 'letter', 'Y', 1),
(2, 'letter', 'O', 1),
(2, 'letter', 'L', 1);

-- Hà Giang (id: 3)
INSERT INTO `location_requirements` (`location_id`, `item_type`, `item_name`, `required_quantity`) VALUES
(3, 'ao', 'Áo', 25),
(3, 'quan', 'Quần', 20),
(3, 'balo', 'Balo', 35),
(3, 'letter', 'I', 1),
(3, 'letter', 'F', 1),
(3, 'letter', 'E', 1);

-- Sapa (id: 4)
INSERT INTO `location_requirements` (`location_id`, `item_type`, `item_name`, `required_quantity`) VALUES
(4, 'ao', 'Áo', 30),
(4, 'quan', 'Quần', 25),
(4, 'balo', 'Balo', 40),
(4, 'letter', 'T', 1),
(4, 'letter', 'O', 1),
(4, 'letter', 'K', 1),
(4, 'letter', 'Y', 1),
(4, 'letter', 'O', 1),
(4, 'letter', 'L', 1),
(4, 'letter', 'I', 1),
(4, 'letter', 'F', 1),
(4, 'letter', 'E', 1);
