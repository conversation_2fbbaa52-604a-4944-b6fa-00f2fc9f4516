# Test Game G2 Changes

## C<PERSON><PERSON> thay đổi đã thực hiện:

### 1. Thêm field `openedBoxes` vào UserSpin entity
- **File**: `src/entities/UserSpin.ts`
- **Thay đổi**: Thêm column `opened_boxes` (TEXT, nullable) để lưu JSON string các box đã mở

### 2. Cập nhật processG2Game
- **File**: `src/services/gameG1.service.ts`
- **Thay đổi**:
  - Cộng `totalSpinToday += 1` khi có winning boxes
  - Lưu các box đã gửi lên vào `openedBoxes` (JSON string)
  - Merge với các box đã mở trước đó, loại bỏ duplicate

### 3. Cập nhật processG1Game
- **File**: `src/services/gameG1.service.ts`
- **Thay đổi**:
  - Cộng `totalSpinToday += 1` khi claim voucher thành công (có couponData)

### 4. Cập nhật initGame
- **File**: `src/services/gameG1.service.ts`
- **Thay đổi**:
  - Reset `totalSpinToday = 0` và `openedBoxes = null` khi sang ngày mới
  - Trả về `opened_boxes` và `received_rewards` cho game G2

### 5. Cập nhật getShareTurnByGameId
- **File**: `src/services/gameG1.service.ts`
- **Thay đổi**:
  - Sử dụng `totalSpinToday` thay vì `totalPlayTurns` để xác định lượt chia sẻ
  - Trả về true nếu đã chơi nhiều hơn số lượt mặc định

### 6. Cập nhật shareForExtraTurn
- **File**: `src/services/gameG1.service.ts`
- **Thay đổi**:
  - Cộng `totalSpinToday += 1` khi chia sẻ thành công
  - Trả về `is_share_turn: true` trong response

### 7. Cập nhật processNoTurns cho game G2
- **File**: `src/services/gameG1.service.ts`
- **Thay đổi**:
  - Trả về `opened_boxes` và `received_rewards` khi hết lượt chơi
  - Cập nhật interface `NoTurnsResult` để bao gồm `opened_boxes`

### 8. Tạo migration
- **File**: `src/migrations/add_opened_boxes_to_user_spins.sql`
- **Nội dung**: ALTER TABLE để thêm column `opened_boxes`

## Test Cases cần kiểm tra:

### 1. Test initGame cho G2
```
GET /api/game/g2/init
Expected response:
{
  "status": "success",
  "user": {...},
  "play_turns": 3,
  "is_share_turn": false,
  "first_play": true,
  "opened_boxes": [1, 2],
  "box_rewards": [
    {
      "box_id": 1,
      "reward": {
        "id": 123,
        "name": "Voucher 100K",
        "image": "voucher.png",
        "type": "voucher",
        "has_voucher": true
      },
      "opened_at": "2024-01-01T10:00:00Z"
    },
    {
      "box_id": 2,
      "reward": {
        "id": null,
        "name": "Chúc bạn may mắn lần sau!",
        "image": null,
        "type": "lose",
        "has_voucher": false
      },
      "opened_at": "2024-01-01T10:00:00Z"
    }
  ]
}
```

### 2. Test claimVoucher cho G2
```
POST /api/game/g2/claim
Body: {"box_ids": [1, 2, 3]}
Expected:
- totalSpinToday tăng lên 1
- openedBoxes được cập nhật với cấu trúc:
[
  {
    "box_id": 1,
    "reward": {
      "id": 123,
      "name": "Voucher 100K",
      "image": "voucher.png",
      "type": "voucher",
      "has_voucher": true
    },
    "opened_at": "2024-01-01T10:00:00Z"
  },
  {
    "box_id": 2,
    "reward": {
      "id": null,
      "name": "Chúc bạn may mắn lần sau!",
      "image": null,
      "type": "lose",
      "has_voucher": false
    },
    "opened_at": "2024-01-01T10:00:00Z"
  }
]
- Lần claim tiếp theo sẽ merge với boxes cũ
```

### 3. Test shareForExtraTurn
```
POST /api/game/g2/share
Body: {"platform": "facebook"}
Expected:
- totalSpinToday tăng lên 1
- is_share_turn: true trong response
```

### 4. Test logic reset hàng ngày
```
- Khi sang ngày mới, totalSpinToday = 0
- openedBoxes = null
- initGame sẽ trả về opened_boxes: []
```

### 5. Test is_share_turn logic
```
- Với game G2 (default 3 lượt):
  - totalSpinToday <= 3: is_share_turn = false
  - totalSpinToday > 3: is_share_turn = true
```

## Database Migration
Cần chạy migration để thêm column:
```sql
ALTER TABLE `user_spins` 
ADD COLUMN `opened_boxes` TEXT NULL COMMENT 'JSON string storing array of box IDs that user has opened in game G2';
```
