import express from 'express'
import { initGuestGameHandler, claimGuestVoucherHandler } from '@/api/guestGame.api'

// Guest Game router
const guestGameRouter = express.Router()

// GET /api/guest-game/:slug/init
guestGameRouter.get('/:slug/init', initGuestGameHandler)

// POST /api/guest-game/:slug/claim
guestGameRouter.post('/:slug/claim', claimGuestVoucherHandler)

// Export router
const router = express.Router()
router.use('/guest-game', guestGameRouter)

export default router 