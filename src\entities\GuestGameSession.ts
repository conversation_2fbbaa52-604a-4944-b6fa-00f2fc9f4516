import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm'

@Entity('guest_game_sessions')
export class GuestGameSession {
  @PrimaryGeneratedColumn()
  id: number

  @Column({ type: 'varchar', length: 36, nullable: false, comment: 'Guest session identifier' })
  guestSessionId: string

  @Column({ type: 'int', nullable: false, comment: 'Campaign ID' })
  campaignId: number

  @Column({ type: 'int', nullable: false, comment: 'Game ID' })
  gameId: number

  @Column({ type: 'json', nullable: true, comment: 'Array of prizes won by guest (only one time)' })
  prizesWon: any[]

  @Column({ type: 'boolean', default: false, comment: 'Whether guest has played (only once)' })
  hasPlayed: boolean

  @Column({ type: 'boolean', default: false, comment: 'Whether data has been merged to user account' })
  isMerged: boolean

  @Column({ type: 'int', nullable: true, comment: 'User ID after merge' })
  mergedUserId: number

  @CreateDateColumn({ type: 'datetime', comment: 'Created at' })
  createdAt: Date

  @UpdateDateColumn({ type: 'datetime', comment: 'Updated at' })
  updatedAt: Date
} 