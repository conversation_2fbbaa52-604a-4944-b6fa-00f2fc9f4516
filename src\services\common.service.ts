import express, { <PERSON><PERSON> } from 'express'
import * as path from 'path'
//import formidable from 'formidable'
import * as formidable from 'formidable'
import * as fs from 'async-file'
import * as isImage from 'is-image'
import { MySqlCnn } from '@/config/config'
import axios from 'axios'
import { VOUCHER_HEADER_TOKEN, API_UPLOAD_AVATAR } from '@/config/constant'
import request from 'request'
import FormData from 'form-data'
import { AppDataSource } from '@/config/config'
import './../globals';
import crypto from 'crypto';


async function uploadImageToStorage(imageUrl) {
  return new Promise((resolve) => {
    const endpoint = API_UPLOAD_AVATAR
    // Assume `imageName` is defined somewhere in your code
    // const images = imageUrl.split('/')
    // const date = new Date()
    // const fileName = Math.floor(date.getTime() / 1000) + images[images.length - 1]

    // Create the data object
    const data = {
      "url": imageUrl
    }

    // Send the request using axios
    axios
      .post(endpoint, data, {
        headers: {
          'Content-Type': 'multipart/form-data',
        }
      })
      .then((response) => {
        console.log(response.data);

        return resolve(response.data)
      })
      .catch((error) => {
        //console.error(error);

        console.log('fail:', error);

        return resolve(error)
      })
  })
}

export const uploadAvatar = async (req, res) => {
  return new Promise((resolve) =>{
    const form = new formidable.IncomingForm(),
    files = [],
    fields = [];

    form.uploadDir = path.join(__dirname, '../../images/');

    form.on('field', function (field, value) {
      //console.log(`This is field[${field}]: ${value}`)

      fields.push([field, value])
    })

    form.on('file', function (field, file) {
      //console.log(`This is filename: ${file}`)

      files.push(file)
    })

    form.on('end', async () => {    
      //const imgs = []
      let url = "";

      for (let i = 0; i < files.length; i++) {
        try{
          const file = files[i]
          const fileExt = path.extname(file.originalFilename)
          const fileName = new Date().getTime().toString() + fileExt
          const filePath = path.join(form.uploadDir, fileName)

          await fs.rename(file.filepath, filePath);

          url = req.protocol + "://" + req.get('Host') + "/images/" + fileName;

          let objTmp = null;
          objTmp = await uploadImageToStorage(url);
          
          if(objTmp.status == true){
            url = objTmp.filePath;
          }
        }
        catch(ex)
        {
          console.log(ex);

          return resolve({
            "status": 0,
            "message": ex
          });
        }
        
        //imgs.push('images/' + fileName);
      }

      return resolve({
        "status": 1,
        "url": url
      });      
    })

    form.parse(req)    
  });
}

export const mysqlQueryData = async (sql) => {
  const connection = MySqlCnn()

  return new Promise((resolve) => {
    //Run the query
    connection.query(sql, function (error, results) {
      if (error) {
        throw error
      }

      connection.destroy()

      return resolve(results)
    })
  })
}

export const voucherPost = async (endPoint, value) => {
  return new Promise((resolve) => {
    axios
      .post(endPoint, value, {
        headers: {
          'cb-2fa-token': VOUCHER_HEADER_TOKEN
        }
      })
      .then((response) => {
        resolve(response.data)
      })
      .catch((error) => {
        resolve(null)
      })
  })
}

class CommonService {
  public static createHmacWithCurrentTime(bizflyProjectToken: string, apiSecret: string, timeNow: number) {
    const message = timeNow + bizflyProjectToken;
    const hmac = crypto.createHmac('sha512', apiSecret);
    hmac.update(message);
    return hmac.digest('hex');
  };

  public static getCurrentTime() {
    return Date.now();
  }
}

export { CommonService }