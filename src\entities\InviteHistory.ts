import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn, Index } from 'typeorm'

@Entity('invite_histories')
@Index(['inviterUserId', 'invitedUserId', 'campaignId', 'gameId'], { unique: true })
export class InviteHistory {
  @PrimaryGeneratedColumn({ type: 'bigint' })
  id: number

  @Column({ name: 'inviter_user_id', type: 'bigint' })
  inviterUserId: number

  @Column({ name: 'invited_user_id', type: 'bigint' })
  invitedUserId: number

  @Column({ name: 'campaign_id', type: 'int' })
  campaignId: number

  @Column({ name: 'game_id', type: 'int' })
  gameId: number

  @Column({ name: 'reward_given', type: 'tinyint', default: 1 })
  rewardGiven: number

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date
} 