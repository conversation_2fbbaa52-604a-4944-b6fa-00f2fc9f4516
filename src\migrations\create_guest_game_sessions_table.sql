-- T<PERSON><PERSON> bảng guest_game_sessions để lưu trữ dữ liệu tạm thời cho guest user chưa đăng nhập
CREATE TABLE IF NOT EXISTS `guest_game_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `guestSessionId` varchar(36) NOT NULL COMMENT 'Guest session identifier',
  `campaignId` int(11) NOT NULL COMMENT 'Campaign ID',
  `gameId` int(11) NOT NULL COMMENT 'Game ID',
  `prizesWon` json DEFAULT NULL COMMENT 'Array of prizes won by guest (only one time)',
  `hasPlayed` tinyint(1) DEFAULT 0 COMMENT 'Whether guest has played (only once)',
  `isMerged` tinyint(1) DEFAULT 0 COMMENT 'Whether data has been merged to user account',
  `mergedUserId` int(11) DEFAULT NULL COMMENT 'User ID after merge',
  `createdAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT 'Created at',
  `updatedAt` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT 'Updated at',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_guest_session_id` (`guestSessionId`),
  KEY `idx_campaign_game` (`campaignId`, `gameId`),
  KEY `idx_merged` (`isMerged`),
  KEY `idx_merged_user` (`mergedUserId`),
  KEY `idx_created_at` (`createdAt`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Guest game sessions for users who haven\'t logged in'; 