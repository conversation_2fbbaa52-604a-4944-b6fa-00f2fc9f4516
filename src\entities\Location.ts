import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('locations', { schema: 'events' })
export class Location extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('varchar', { name: 'name', length: 255 })
  name: string

  @Column('text', { name: 'rules', nullable: true })
  rules: string

  @Column('int', { name: 'active', default: 1 })
  active: number

  @Column('int', { name: 'sort_order', default: 0 })
  sortOrder: number

  @Column('varchar', { name: 'type', length: 50, default: 'game3' })
  type: string // để phân biệt loại location (game3, game4, etc.)

  @Column('timestamp', { name: 'created_at' })
  createdAt: Date

  @Column('timestamp', { name: 'updated_at' })
  updatedAt: Date
}
