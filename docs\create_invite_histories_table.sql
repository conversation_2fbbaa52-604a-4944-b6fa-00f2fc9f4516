-- Migration: Create invite_histories table
-- <PERSON><PERSON><PERSON> đích: Track invite relationship giữa inviter và invited user để tránh duplicate reward

CREATE TABLE IF NOT EXISTS `invite_histories` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `inviter_user_id` bigint(20) NOT NULL COMMENT 'ID người mời',
  `invited_user_id` bigint(20) NOT NULL COMMENT 'ID người được mời',
  `campaign_id` int(11) NOT NULL COMMENT 'ID campaign',
  `game_id` int(11) NOT NULL COMMENT 'ID game',
  `reward_given` tinyint(1) NOT NULL DEFAULT 1 COMMENT 'Đã tặng thưởng hay chưa (1=yes, 0=no)',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMAR<PERSON>Y (`id`),
  UNIQUE KEY `unique_invite_per_game` (`inviter_user_id`, `invited_user_id`, `campaign_id`, `game_id`),
  KEY `idx_inviter_user` (`inviter_user_id`),
  KEY `idx_invited_user` (`invited_user_id`),
  KEY `idx_campaign_game` (`campaign_id`, `game_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Lịch sử mời bạn bè - tránh duplicate reward'; 