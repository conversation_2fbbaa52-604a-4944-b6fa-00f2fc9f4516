import { Column, Entity, PrimaryGeneratedColumn, ManyToOne, JoinColumn, Index } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'
import { TblUsers } from './TblUsers'
import { Location } from './Location'

@Entity('game3_user_items', { schema: 'events' })
@Index(['userId', 'campaignId', 'gameId', 'locationId', 'itemType'], { unique: true })
export class Game3UserItems extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('int', { name: 'user_id' })
  userId: number

  @Column('int', { name: 'campaign_id' })
  campaignId: number

  @Column('int', { name: 'game_id' })
  gameId: number

  @Column('int', { name: 'location_id' })
  locationId: number

  @Column('varchar', { name: 'item_type', length: 50 })
  itemType: string

  @Column('int', { name: 'quantity', default: 0 })
  quantity: number

  @ManyToOne(() => TblUsers)
  @JoinColumn({ name: 'user_id' })
  user: TblUsers

  @ManyToOne(() => Location)
  @JoinColumn({ name: 'location_id' })
  location: Location
}
