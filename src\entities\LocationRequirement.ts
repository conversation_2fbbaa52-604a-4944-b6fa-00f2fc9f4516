import { Column, <PERSON>tity, PrimaryGeneratedColumn, ManyToOne, JoinColumn } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'
import { Location } from './Location'

@Entity('location_requirements', { schema: 'events' })
export class LocationRequirement extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'int', name: 'id' })
  id: number

  @Column('int', { name: 'location_id' })
  locationId: number

  @Column('varchar', { name: 'item_type', length: 50 })
  itemType: string

  @Column('varchar', { name: 'item_name', length: 255 })
  itemName: string

  @Column('int', { name: 'required_quantity' })
  requiredQuantity: number

  @ManyToOne(() => Location)
  @JoinColumn({ name: 'location_id' })
  location: Location

  @Column('timestamp', { name: 'created_at' })
  createdAt: Date

  @Column('timestamp', { name: 'updated_at' })
  updatedAt: Date
}
