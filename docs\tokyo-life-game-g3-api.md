
# Tokyo Life Game - API Documentation - G3

## Authentication
All requests require authentication via <PERSON><PERSON>.

**Header Example:**

```

Authorization: Bearer <token>

````

---

## 1. GET `/api/game/init`

**Description:** Initialize game, preload user data and game UI.

**Response:**
```json
{
  "status": "success",
  "user": {
    "id": 123,
    "name": "<PERSON>uyen <PERSON>"
  },
  "play_turns": 3,
  "letters": ["T", "O"],
  "locations": [
    { "id": 1, "name": "<PERSON><PERSON><PERSON><PERSON>" , "star": 1, "received": [{"reward_id": 1,"name": "Voucher 20K", "image": "ma_qua_01"}]},
    { "id": 2, "name": "Điện Biên", "star": 1, "received": [{"reward_id": 1,"name": "Voucher 20K", "image": "ma_qua_01"}]}
  ]
}
````
---

## 2. POST `/api/game/rules`

**<PERSON><PERSON><PERSON> thị thể lệ chương trình.**

#### Request
```json
{
  "location_id": 1
}
```

#### Response
```json
{
  "status": "success",
  "rules": "Nội dung chi tiết dạng HTML thể lệ chương trình cho Tuyên Quang."
}
```

---

## 3. `GET /api/game/locations`

**Danh sách các tỉnh thành có thể chọn.**

#### Response
```json
{
  "status": "success",
  "locations": [
    { "id": 1, "name": "Tuyên Quang" },
    { "id": 2, "name": "Điện Biên" }
  ]
}

```

---

## 4. `GET /api/game/location-detail/:id`

**Lấy yêu cầu combo vật phẩm của từng tỉnh thành.**

#### Response
```json
{
  "status": "success",
  "location_id": 1,
  "location_name": "Tuyên Quang",
  "required_items": [
    { "type": "ao", "name": "Áo", "required": 15 },
    { "type": "quan", "name": "Quần", "required": 10 },
    { "type": "balo", "name": "Balo", "required": 25 }
  ]
}

```

---

## 5. `GET /api/game/play/:location_id`

**Bắt đầu game tại 1 tỉnh thành, trả về danh sách vật phẩm & chữ cái có thể xuất hiện.**

#### Response
```json
{
  "status": "success",
  "play_turns": 2,
  "spawn_items": [
    { "type": "ao", "image": "item_ao" },
    { "type": "quan", "image": "item_quan" },
    { "type": "balo", "image": "item_balo" }
  ],
  "spawn_letters": ["T", "O"],
  "code": "unique_code_123"
}


```

---

## 6. `POST /api/game/end`

**Kết thúc lượt chơi, xác nhận số vật phẩm và chữ cái thu thập được.**

#### Request
```json
{
  "code": "unique_code_123",
  "location_id": 1,
  "duration": 120,
  "distance": 300,
  "collected_items": {
    "ao": 2,
    "quan": 3,
    "balo": 1
  },
  "collected_letters": ["T"]
}
```

#### Response
```json
{
  "status": "success",
  "play_turns": 2,
  "time_left": 120,
  "distance_covered": 300,
  "collected_items": {
    "ao": 5,
    "quan": 8,
    "balo": 7
  },
  "collected_letters": ["T", "O", "K"]
}
```

---

## 7. POST `/api/game/share`

**Description:** Share to receive 1 extra play turn (1 time per day).

**Request:**

```json
{
  "platform": "facebook"
}
```

**Response:**

```json
{
  "status": "success",
  "message": "Bạn đã nhận thêm 1 lượt chơi!",
  "play_turns": 1
}
```

**If already shared today:**

```json
{
  "status": "already_shared",
  "message": "Bạn đã chia sẻ và nhận lượt hôm nay rồi"
}
```

---
