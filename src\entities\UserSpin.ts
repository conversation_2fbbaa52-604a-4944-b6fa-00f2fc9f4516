import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm'
import { BaseEntity } from './base/BaseEntity'

@Entity('user_spins')
export class UserSpin extends BaseEntity {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id' })
  id: number

  @Column('bigint', { name: 'user_id' })
  userId: number

  @Column('int', { name: 'spin_counts' })
  spinCounts: number

  @Column('timestamp', { name: 'created_at' })
  createdAt: Date

  @Column('timestamp', { name: 'updated_at' })
  updatedAt: Date

  @Column('timestamp', { name: 'last_request', nullable: true })
  lastRequest: Date | null

  @Column('int', { name: 'campaign_id' })
  campaignId: number

  @Column('int', { name: 'game_id' })
  gameId: number

  @Column('timestamp', { name: 'last_request_facebook', nullable: true })
  lastRequestFacebook: Date | null

  @Column('int', { name: 'invite_count', default: 0 })
  inviteCount: number

  @Column('int', { name: 'total_spin_today', default: 0 })
  totalSpinToday: number
} 