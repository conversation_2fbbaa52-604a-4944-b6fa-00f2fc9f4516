import express from 'express'
import userRouter from './user.route'
import authRouter from './auth.route'
import gameG1Router from './gameG1.route'
import gameG3Router from './gameG3.route'
import luckyWheelRouter from './luckyWheel.route'
import guestGameRouter from './guestGame.route'

const router = express.Router()

router.use(userRouter)
router.use(authRouter)
router.use(gameG1Router)
router.use(gameG3Router)
router.use(luckyWheelRouter)
router.use(guestGameRouter)

export const apiRouter = router
