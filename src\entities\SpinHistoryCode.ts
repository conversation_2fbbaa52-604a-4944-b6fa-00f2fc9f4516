import { <PERSON>tity, PrimaryGeneratedColumn, Column, ManyTo<PERSON>ne, Join<PERSON><PERSON>umn } from 'typeorm'
import { LuckyPrize } from './LuckyPrize'

@Entity('spin_histories_code')
export class SpinHistoryCode {
  @PrimaryGeneratedColumn({ type: 'bigint', name: 'id' })
  id: number

  @Column('bigint', { name: 'user_id' })
  userId: number

  @Column('bigint', { name: 'prize_id' })
  prizeId: number

  @ManyToOne(() => LuckyPrize)
  @JoinColumn({ name: 'prize_id' })
  prize: LuckyPrize

  @Column('timestamp', { name: 'spin_time' })
  spinTime: Date

  @Column('text', { name: 'description', nullable: true })
  description: string

  @Column('text', { name: 'address', nullable: true })
  address: string

  @Column('timestamp', { name: 'created_at' })
  createdAt: Date

  @Column('timestamp', { name: 'claimed_at', nullable: true })
  claimedAt: Date | null

  @Column('timestamp', { name: 'updated_at' })
  updatedAt: Date

  @Column('timestamp', { name: 'deleted_at', nullable: true })
  deletedAt: Date | null

  @Column('int', { name: 'status', default: 0 })
  status: number

  @Column('int', { name: 'campaign_id' })
  campaignId: number

  @Column('int', { name: 'game_id' })
  gameId: number

  @Column('int', { name: 'biz_coupon_id', nullable: true })
  bizCouponId: number | null

  @Column('text', { name: 'voucher_code', nullable: true })
  voucherCode: string | null

  @Column('text', { name: 'voucher_name', nullable: true })
  voucherName: string | null

  @Column('text', { name: 'voucher_link', nullable: true })
  voucherLink: string | null
} 