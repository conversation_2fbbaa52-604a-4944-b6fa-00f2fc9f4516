# Tokyo Life Game G3 - Implementation Guide

## Tổng quan

API Game3 đã được triển khai hoàn chỉnh theo yêu cầu trong tài liệu `tokyo-life-game-g3-api.md`. Dưới đây là hướng dẫn chi tiết về cách sử dụng và kiểm tra API.

## Cấu trúc Database (Tối ưu)

### Các bảng đã tạo/sử dụng:

1. **locations** - Danh sách các tỉnh thành (tái sử dụng cho tương lai)
2. **location_requirements** - <PERSON><PERSON><PERSON> cầu vật phẩm cho từng tỉnh
3. **user_spins** - Tiến trình chơi của user (đã có, thêm fields mới)
4. **game3_user_items** - Vật phẩm đã thu thập của user
5. **game3_play_sessions** - <PERSON><PERSON><PERSON> chơi game
6. **prizes** + **spin_histories** - <PERSON><PERSON><PERSON> thưởng (tái sử dụng tables hiện có)

### Migration

Chạy file SQL sau để tạo database:
```bash
mysql -u username -p database_name < src/migrations/game3_setup.sql
```

## API Endpoints

### 1. GET `/api/game3/init`
Khởi tạo game, tải dữ liệu user và UI.

**Headers:**
```
Authorization: Bearer <token>
```

**Response:**
```json
{
  "status": "success",
  "user": {
    "id": 123,
    "name": "Nguyen Van A"
  },
  "play_turns": 3,
  "letters": ["T", "O"],
  "locations": [
    {
      "id": 1,
      "name": "Tuyên Quang",
      "star": 1,
      "received": [{"reward_id": 1, "name": "Voucher 20K", "image": "ma_qua_01"}]
    }
  ]
}
```

### 2. POST `/api/game/rules`
Hiển thị thể lệ chương trình.

**Request:**
```json
{
  "location_id": 1
}
```

**Response:**
```json
{
  "status": "success",
  "rules": "Nội dung chi tiết dạng HTML thể lệ chương trình cho Tuyên Quang."
}
```

### 3. GET `/api/game/locations`
Danh sách các tỉnh thành có thể chọn.

**Response:**
```json
{
  "status": "success",
  "locations": [
    { "id": 1, "name": "Tuyên Quang" },
    { "id": 2, "name": "Điện Biên" }
  ]
}
```

### 4. GET `/api/game/location-detail/:id`
Lấy yêu cầu combo vật phẩm của từng tỉnh thành.

**Response:**
```json
{
  "status": "success",
  "location_id": 1,
  "location_name": "Tuyên Quang",
  "required_items": [
    { "type": "ao", "name": "Áo", "required": 15 },
    { "type": "quan", "name": "Quần", "required": 10 },
    { "type": "balo", "name": "Balo", "required": 25 }
  ]
}
```

### 5. GET `/api/game3/play/:location_id`
Lấy thông tin về địa điểm chơi (không trừ lượt chơi).

**Response:**
```json
{
  "status": "success",
  "play_turns": 2,
  "spawn_items": [
    { "type": "ao", "image": "item_ao" },
    { "type": "quan", "image": "item_quan" },
    { "type": "balo", "image": "item_balo" }
  ],
  "spawn_letters": ["T", "O"],
  "code": "unique_code_123"
}
```

### 6. POST `/api/game3/end?context=<context>`
Kết thúc lượt chơi (trừ lượt chơi tại đây).

**Validation:**
- Kiểm tra `collected_items` có tồn tại trong `location_requirements` của `location_id`
- Kiểm tra `collected_letters` có tồn tại trong `location_requirements` của `location_id`

**Reward System:**
- **Hoàn thành location**: Thu thập đủ tất cả requirements → 3 sao + tặng quà
- **Hoàn thành một phần**: Tính sao theo tỉ lệ (1-2 sao)
- **Hoàn thành TOKYOLIFE**: Thu thập đủ tất cả chữ cái T-O-K-Y-O-L-I-F-E → tặng quà đặc biệt
- **Context parameter**: Dùng để lọc prizes theo context (tương tự gameG1)

**Request:**
```json
{
  "code": "unique_code_123",
  "location_id": 1,
  "duration": 120,
  "distance": 300,
  "collected_items": {
    "ao": 2,
    "quan": 3,
    "balo": 1
  },
  "collected_letters": ["T"]
}
```

**Response:**
```json
{
  "status": "success",
  "play_turns": 2,
  "time_left": 120,
  "distance_covered": 300,
  "collected_items": {
    "ao": 5,
    "quan": 8,
    "balo": 7
  },
  "collected_letters": ["T", "O", "K"],
  "stars": 3,
  "location_completed": true,
  "tokyo_life_completed": false,
  "rewards": {
    "location_reward": {
      "prize": {
        "id": 1,
        "name": "Voucher 50K",
        "type": "voucher"
      },
      "coupon": {
        "couponCode": "ABC123",
        "couponName": "Voucher 50K",
        "qrCodeLink": "https://...",
        "end_date": "2024-12-31"
      },
      "message": "Bạn nhận được Voucher 50K"
    },
    "tokyo_life_reward": null
  }
}
```

**Error Responses:**
```json
{
  "status": "error",
  "message": "Item 'invalid_item' không tồn tại trong location 1",
  "code": 1
}
```

```json
{
  "status": "error",
  "message": "Letter 'X' không tồn tại trong location 1",
  "code": 1
}
```

### 7. POST `/api/game/g3/share`
Share để nhận thêm lượt chơi (đã có sẵn từ gameG1).

## Cách test API

1. **Khởi tạo game:**
   ```bash
   curl -X GET "http://localhost:3000/api-v1/game/g3/init" \
   -H "Authorization: Bearer <token>"
   ```

2. **Lấy danh sách locations:**
   ```bash
   curl -X GET "http://localhost:3000/api-v1/game/locations" \
   -H "Authorization: Bearer <token>"
   ```

3. **Bắt đầu chơi:**
   ```bash
   curl -X GET "http://localhost:3000/api-v1/game3/play/1" \
   -H "Authorization: Bearer <token>"
   ```

4. **Kết thúc game:**
   ```bash
   curl -X POST "http://localhost:3000/api-v1/game3/end?context=special" \
   -H "Authorization: Bearer <token>" \
   -H "Content-Type: application/json" \
   -d '{"code":"session_code","location_id":1,"duration":120,"distance":300,"collected_items":{"ao":2},"collected_letters":["T"]}'
   ```

## Thay đổi Logic Game

### Chữ cái là Items:
- Chữ cái được coi như vật phẩm và lưu trong `location_requirements`
- `spawnItems` và `spawnLetters` được lấy từ `location_requirements` theo `location_id`
- Phân biệt items thường và letters bằng `item_type = 'letter'`

### API Play vs End:
- **API Play**: Chỉ lấy thông tin về địa điểm, không trừ lượt chơi
- **API End**: Kết thúc lượt chơi và trừ lượt chơi tại đây

### Lưu trữ Letters:
- `collectedLetters` được lưu vào `game3_user_items` như các item khác
- Sử dụng `itemType = 'letter_X'` để phân biệt từng chữ cái
- Không còn lưu trong `user_spins.collected_letters`

### Reward System:
- **Location Completion**: Khi thu thập đủ tất cả requirements của 1 location
  - Tự động tặng 1 phần quà random theo tỉ lệ winrate
  - Tính 3 sao cho location đó
  - Sử dụng context parameter để lọc prizes

- **Partial Completion**: Khi chưa thu thập đủ requirements
  - Tính sao theo tỉ lệ: ≥100% = 3 sao, ≥80% = 2 sao, ≥50% = 1 sao
  - Không tặng quà

- **TOKYOLIFE Completion**: Khi thu thập đủ tất cả chữ cái T-O-K-Y-O-L-I-F-E
  - Tự động tặng 1 phần quà đặc biệt
  - Không cần trong cùng 1 location, tính theo toàn bộ gameId
  - Chỉ cần mỗi chữ cái ít nhất 1 lần

- **Prize Logic**: Tương tự gameG1
  - Random theo winrate và số lượng còn lại
  - Gọi API Bizfly để lấy coupon thật
  - Lưu vào SpinHistory để tracking

## Lưu ý

- API đã được tích hợp hoàn chỉnh vào hệ thống hiện tại
- Sử dụng cùng authentication system với các API khác
- Logging được tích hợp để theo dõi hoạt động
- Database schema tương thích với cấu trúc hiện tại
- API share đã có sẵn từ gameG1, không cần implement lại

## Files đã tạo/sửa đổi

### Entities (Tối ưu):
- `src/entities/Location.ts` (thay thế Game3Location)
- `src/entities/LocationRequirement.ts` (thay thế Game3LocationRequirement)
- `src/entities/UserSpin.ts` (đã có, thêm fields mới)
- `src/entities/Game3UserItems.ts`
- `src/entities/Game3PlaySession.ts`
- `src/entities/LuckyPrize.ts` + `src/entities/SpinHistory.ts` (tái sử dụng)

### Services:
- `src/services/gameG3.service.ts`

### API:
- `src/api/gameG3.api.ts`

### Routes:
- `src/routes/gameG3.route.ts`

### Migration:
- `src/migrations/game3_setup.sql`

### Index files updated:
- `src/entities/index.ts`
- `src/services/index.ts`
- `src/api/index.ts`
- `src/routes/api.route.ts`
