import express from 'express'
import { gameG3Service } from '@/services'
import { AuthorizedUserRequest } from '@/middlewares/auth'
import { AppDataSource } from '@/config/config'
import { LogActivity } from '@/entities/LogActivity'

// Helper function để lấy thời gian GMT+7
const getNowGMT7 = () => {
  return new Date(new Date().getTime() + 7 * 60 * 60 * 1000)
}

// Helper function để log activity
const logActivityRepo = () => AppDataSource.getRepository(LogActivity)

async function logActivity(
  userId: number, 
  name: string, 
  url: string, 
  method: string, 
  requestData: any, 
  result: any, 
  ip?: string, 
  agent?: string
) {
  try {
    const log = new LogActivity()
    log.user_id = userId
    log.name = name
    log.message = `GameG3 API ${name} - Status: ${result?.status || 'unknown'}`
    log.full_message = JSON.stringify(result)
    log.url = url
    log.method = method
    log.ip = ip
    log.agent = agent
    log.form_data = JSON.stringify(requestData)
    log.created_at = getNowGMT7()
    log.updated_at = getNowGMT7()
    
    await logActivityRepo().save(log)
  } catch (error) {
    console.error('Error logging activity:', error)
  }
}

/**
 * 1. GET /api/game/init
 * Initialize game, preload user data and game UI
 */
const initGameHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const result = await gameG3Service.initGame(req)
    
    await logActivity(
      req.authorizedUser.user.id,
      'init',
      req.originalUrl,
      req.method,
      { },
      result,
      req.ip,
      req.get('User-Agent')
    )
    
    res.json(result)
  } catch (error: any) {
    console.error('Error in initGameHandler:', error)
    
    await logActivity(
      req.authorizedUser?.user?.id || 0,
      'init',
      req.originalUrl,
      req.method,
      { slug: req.params.slug },
      { status: 'error', message: error.message },
      req.ip,
      req.get('User-Agent')
    )
    
    res.status(error.statusCode || 500).json({
      status: 'error',
      message: error.message || 'Internal server error'
    })
  }
}

/**
 * 2. POST /api/game/rules
 * Hiển thị thể lệ chương trình
 */
const getRulesHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const { locationId } = req.body
    
    if (!locationId) {
      return res.status(400).json({
        status: 'error',
        message: 'location_id is required'
      })
    }
    
    const result = await gameG3Service.getRules(locationId)
    
    await logActivity(
      req.authorizedUser.user.id,
      'rules',
      req.originalUrl,
      req.method,
      { locationId },
      result,
      req.ip,
      req.get('User-Agent')
    )
    
    res.json(result)
  } catch (error: any) {
    console.error('Error in getRulesHandler:', error)
    
    await logActivity(
      req.authorizedUser?.user?.id || 0,
      'rules',
      req.originalUrl,
      req.method,
      req.body,
      { status: 'error', message: error.message },
      req.ip,
      req.get('User-Agent')
    )
    
    res.status(error.statusCode || 500).json({
      status: 'error',
      message: error.message || 'Internal server error'
    })
  }
}

/**
 * 3. GET /api/game/locations
 * Danh sách các tỉnh thành có thể chọn
 */
const getLocationsHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const result = await gameG3Service.getLocations()
    
    await logActivity(
      req.authorizedUser.user.id,
      'locations',
      req.originalUrl,
      req.method,
      {},
      result,
      req.ip,
      req.get('User-Agent')
    )
    
    res.json(result)
  } catch (error: any) {
    console.error('Error in getLocationsHandler:', error)
    
    await logActivity(
      req.authorizedUser?.user?.id || 0,
      'locations',
      req.originalUrl,
      req.method,
      {},
      { status: 'error', message: error.message },
      req.ip,
      req.get('User-Agent')
    )
    
    res.status(error.statusCode || 500).json({
      status: 'error',
      message: error.message || 'Internal server error'
    })
  }
}

/**
 * 4. GET /api/game/location-detail/:id
 * Lấy yêu cầu combo vật phẩm của từng tỉnh thành
 */
const getLocationDetailHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const { id } = req.params
    const locationId = parseInt(id)
    
    if (!locationId || isNaN(locationId)) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid location ID'
      })
    }
    
    const result = await gameG3Service.getLocationDetail(locationId)
    
    await logActivity(
      req.authorizedUser.user.id,
      'location-detail',
      req.originalUrl,
      req.method,
      { id: locationId },
      result,
      req.ip,
      req.get('User-Agent')
    )
    
    res.json(result)
  } catch (error: any) {
    console.error('Error in getLocationDetailHandler:', error)
    
    await logActivity(
      req.authorizedUser?.user?.id || 0,
      'location-detail',
      req.originalUrl,
      req.method,
      { id: req.params.id },
      { status: 'error', message: error.message },
      req.ip,
      req.get('User-Agent')
    )
    
    res.status(error.statusCode || 500).json({
      status: 'error',
      message: error.message || 'Internal server error'
    })
  }
}

/**
 * 5. GET /api/game/play/:location_id
 * Bắt đầu game tại 1 tỉnh thành
 */
const playGameHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const { location_id: locationIdParam } = req.params
    const locationId = parseInt(locationIdParam)
    
    if (!locationId || isNaN(locationId)) {
      return res.status(400).json({
        status: 'error',
        message: 'Invalid location ID'
      })
    }
    
    const result = await gameG3Service.playGame(req, locationId)
    
    await logActivity(
      req.authorizedUser.user.id,
      'play',
      req.originalUrl,
      req.method,
      { location_id: locationId },
      result,
      req.ip,
      req.get('User-Agent')
    )
    
    res.json(result)
  } catch (error: any) {
    console.error('Error in playGameHandler:', error)
    
    await logActivity(
      req.authorizedUser?.user?.id || 0,
      'play',
      req.originalUrl,
      req.method,
      { slug: req.params.slug, location_id: req.params.location_id },
      { status: 'error', message: error.message },
      req.ip,
      req.get('User-Agent')
    )
    
    res.status(error.statusCode || 500).json({
      status: 'error',
      message: error.message || 'Internal server error'
    })
  }
}

/**
 * 6. POST /api/game/end
 * Kết thúc lượt chơi
 */
const endGameHandler = async (req: AuthorizedUserRequest, res: express.Response) => {
  try {
    const endGameData = req.body

    // Thêm context từ query parameter nếu có
    if (req.query.context) {
      endGameData.context = req.query.context as string
    }

    const result = await gameG3Service.endGame(req, endGameData)
    
    await logActivity(
      req.authorizedUser.user.id,
      'end',
      req.originalUrl,
      req.method,
      endGameData,
      result,
      req.ip,
      req.get('User-Agent')
    )
    
    res.json(result)
  } catch (error: any) {
    console.error('Error in endGameHandler:', error)
    
    await logActivity(
      req.authorizedUser?.user?.id || 0,
      'end',
      req.originalUrl,
      req.method,
      req.body,
      { status: 'error', message: error.message },
      req.ip,
      req.get('User-Agent')
    )
    
    res.status(error.statusCode || 500).json({
      status: 'error',
      message: error.message || 'Internal server error'
    })
  }
}

export {
  initGameHandler,
  getRulesHandler,
  getLocationsHandler,
  getLocationDetailHandler,
  playGameHandler,
  endGameHandler
}
