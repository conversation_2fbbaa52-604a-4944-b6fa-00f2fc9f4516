import { AuthorizedUserRequest } from '@/middlewares/auth'
import { BaseSearchRequest } from './base'
import { TblUsers } from '@/entities/TblUsers'
import { StringDecoder } from 'node:string_decoder'

export interface CreateGroupRequest extends AuthorizedUserRequest {
  name: string
  description: StringDecoder
}

export interface UpdateGroupRequest extends AuthorizedUserRequest {
  id: number
  name: string
  description: string
  users: TblUsers[]
}

export interface GroupRequest extends AuthorizedUserRequest {
  id: number
}

export interface GroupSearchRequest extends BaseSearchRequest {
  name: string
}

export interface UserGroupRequest extends AuthorizedUserRequest {
  userId: number
}
