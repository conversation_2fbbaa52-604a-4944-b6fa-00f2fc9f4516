import express from 'express'
import { gameG1API } from '@/api'
import { authenticate } from '@/middlewares/auth'

// Game G1 router
const gameG1Router = express.Router()

// GET /api/game/:slug/init
gameG1Router.get('/:slug/init', authenticate, gameG1API.initGameHandler)

// GET /api/game/:slug/play  
gameG1Router.get('/:slug/play', authenticate, gameG1API.playGameHandler)

// POST /api/game/:slug/claim
gameG1Router.post('/:slug/claim', authenticate, gameG1API.claimVoucherHandler)

// POST /api/game/:slug/share
gameG1Router.post('/:slug/share', authenticate, gameG1API.shareForExtraTurnHandler)

gameG1Router.get('/:slug/history', authenticate, gameG1API.getGameHistoryHandler)

gameG1Router.get('/voucher-storage', authenticate, gameG1API.getVoucherStorageHandler)

// POST /api/game/:slug/invite-reward
gameG1Router.post('/:slug/invite', authenticate, gameG1API.giveInviteRewardHandler)

gameG1Router.post('/draw-gift', authenticate, gameG1API.drawGiftHandler)

// POST /api/game/merge-guest-data - Merge guest data khi user đăng nhập
gameG1Router.post('/merge-guest-data', authenticate, gameG1API.mergeGuestDataHandler)

// Export router
const router = express.Router()
router.use('/game', gameG1Router)

export default router 