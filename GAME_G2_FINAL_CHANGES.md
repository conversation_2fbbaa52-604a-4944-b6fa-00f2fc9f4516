# Game G2 - Thay đổi cuối cùng về mapping box và reward

## Vấn đề đã được giải quyết:

### 1. **Vấn đề ban đầu:**
- Khi init game G2, `opened_boxes` và `received_rewards` bị tách rời
- Không thể biết box nào tương ứng với reward nào
- Khi claim game G2, không phải lúc nào cũng có voucher (có thể chỉ là "chúc bạn may mắn lần sau")

### 2. **Giải pháp:**
- Thay đổi cấu trúc lưu trữ trong `openedBoxes` từ array đơn giản thành array object phức tạp
- Mỗi object chứa đầy đủ thông tin về box và reward tương ứng
- Không cần phải map từ SpinHistory nữa

## C<PERSON><PERSON> trú<PERSON> dữ liệu mới:

### openedBoxes trong database (JSON string):
```json
[
  {
    "box_id": 1,
    "reward": {
      "id": 123,
      "name": "Voucher 100K",
      "image": "voucher.png",
      "type": "voucher",
      "has_voucher": true
    },
    "opened_at": "2024-01-01T10:00:00Z"
  },
  {
    "box_id": 2,
    "reward": {
      "id": null,
      "name": "Chúc bạn may mắn lần sau!",
      "image": null,
      "type": "lose",
      "has_voucher": false
    },
    "opened_at": "2024-01-01T10:00:00Z"
  }
]
```

## API Response mới:

### initGame cho G2:
```json
{
  "status": "success",
  "user": {...},
  "play_turns": 3,
  "is_share_turn": false,
  "first_play": true,
  "opened_boxes": [1, 2],
  "box_rewards": [
    {
      "box_id": 1,
      "reward": {
        "id": 123,
        "name": "Voucher 100K",
        "image": "voucher.png",
        "type": "voucher",
        "has_voucher": true
      },
      "opened_at": "2024-01-01T10:00:00Z"
    },
    {
      "box_id": 2,
      "reward": {
        "id": null,
        "name": "Chúc bạn may mắn lần sau!",
        "image": null,
        "type": "lose",
        "has_voucher": false
      },
      "opened_at": "2024-01-01T10:00:00Z"
    }
  ]
}
```

### processNoTurns cho G2:
```json
{
  "status": "no_turns",
  "message": "Bạn đã hết lượt chơi hôm nay",
  "boxes": [...],
  "opened_boxes": [1, 2],
  "box_rewards": [
    {
      "box_id": 1,
      "reward": {
        "id": 123,
        "name": "Voucher 100K",
        "image": "voucher.png",
        "type": "voucher",
        "has_voucher": true
      },
      "opened_at": "2024-01-01T10:00:00Z"
    }
  ],
  "share_remaining": 1
}
```

## Thay đổi code chính:

### 1. processG2Game - Lưu box với reward:
```typescript
// Lưu các box đã gửi lên vào openedBoxes với thông tin reward
const currentOpenedBoxes = userSpin.openedBoxes ? JSON.parse(userSpin.openedBoxes) : []

// Tạo thông tin box với reward tương ứng
const newBoxData = sentBoxIds.map(boxId => {
  const box = boxes.find(b => b.id === boxId)
  const existingBox = currentOpenedBoxes.find((b: any) => b.box_id === boxId)
  
  if (existingBox) {
    return existingBox // Giữ nguyên nếu đã có
  }
  
  return {
    box_id: boxId,
    reward: {
      id: box?.reward?.reward_id || null,
      name: box?.reward?.name || "Chúc bạn may mắn lần sau!",
      image: box?.reward?.image || null,
      type: box?.prizeData?.type || "lose",
      has_voucher: winningBoxes.includes(boxId) && box?.prizeData && box.prizeData.type !== 'lose'
    },
    opened_at: getNowGMT7()
  }
})

// Merge với dữ liệu cũ
const allBoxData = [...currentOpenedBoxes, ...newBoxData]
userSpin.openedBoxes = JSON.stringify(allBoxData)
```

### 2. initGame - Trả về box_rewards:
```typescript
if (gameIdSlug === 'g2') {
  // Lấy danh sách các box đã mở với thông tin reward
  const boxRewards = userSpin.openedBoxes ? JSON.parse(userSpin.openedBoxes) : []
  
  // Tách ra thành 2 array riêng biệt để tương thích với frontend
  const openedBoxes = boxRewards.map((item: any) => item.box_id)
  
  responseData.opened_boxes = openedBoxes
  responseData.box_rewards = boxRewards
}
```

### 3. processNoTurns - Trả về box_rewards:
```typescript
// Lấy thông tin các box đã mở với reward tương ứng
const boxRewards = userSpin.openedBoxes ? JSON.parse(userSpin.openedBoxes) : []

// Tách ra thành 2 array riêng biệt để tương thích với frontend
const openedBoxes = boxRewards.map((item: any) => item.box_id)

return {
  status: 'no_turns',
  message: 'Bạn đã hết lượt chơi hôm nay',
  boxes,
  opened_boxes: openedBoxes,
  box_rewards: boxRewards,
  share_remaining: shareRemaining
}
```

## Lợi ích của thay đổi:

1. **Mapping chính xác**: Box và reward được map chính xác với nhau
2. **Xử lý "may mắn lần sau"**: Có thể phân biệt box có voucher và box không có voucher
3. **Đơn giản hóa logic**: Không cần query SpinHistory để map dữ liệu
4. **Tương thích ngược**: Vẫn trả về `opened_boxes` array để tương thích với frontend hiện tại
5. **Thông tin đầy đủ**: `box_rewards` chứa đầy đủ thông tin về từng box đã mở

## Frontend có thể sử dụng:

- `opened_boxes`: Array các box ID đã mở (tương thích với code cũ)
- `box_rewards`: Array chi tiết về từng box và reward tương ứng (cho tính năng mới)

Với cấu trúc này, frontend có thể dễ dàng hiển thị:
- Box nào đã mở
- Box nào có voucher, box nào chỉ là "may mắn lần sau"
- Thông tin chi tiết về reward của từng box
- Thời gian mở box
