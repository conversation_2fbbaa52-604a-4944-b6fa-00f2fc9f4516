## 🌐 API cho Guest User (<PERSON><PERSON><PERSON> đăng nhập)

Guest user có thể chơi game mà không cần đăng nhập. Dữ liệu sẽ được lưu tạm thời và có thể merge vào tài khoản user khi đăng nhập.

### 1. 🚀 **Initialize Guest Game G1**

Khởi tạo session game cho guest user. Mỗi guest chỉ được chơi 1 lần.

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/guest-game/g1/init'
```

**Response Success:**
```json
{
    "status": "success",
    "guest_session_id": "550e8400-e29b-41d4-a716-446655440000",
    "play_turns": 1
}
```

### 2. 🎁 **Claim Guest Voucher G1**

Guest user claim voucher với session ID đã nhận được từ init.

**Request Body:**
```json
{
  "guest_session_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/guest-game/g1/claim' \
--header 'Content-Type: application/json' \
--data '{
    "guest_session_id": "550e8400-e29b-41d4-a716-446655440000"
}'
```

**Response Success (Lần đầu chơi):**
```json
{
    "status": "success",
    "message": "Bạn đã nhận được Voucher 100K! Đăng nhập để nhận phần thưởng.",
    "guest_session_id": "550e8400-e29b-41d4-a716-446655440000",
    "play_turns": 0,
    "prize": {
        "id": 39,
        "name": "Voucher 100K",
        "type": "voucher"
    }
}
```

**Response (Đã chơi rồi):**
```json
{
    "status": "error",
    "message": "Guest đã chơi rồi. Mỗi guest chỉ được chơi 1 lần."
}
```

**Response Error:**
```json
{
    "status": "error",
    "message": "Guest session không tồn tại hoặc đã được merge"
}
```

### 3. 🔄 **Merge Guest Data**

Khi user đăng nhập, merge dữ liệu guest vào tài khoản user để nhận coupon thật.

**Request Body:**
```json
{
  "guest_session_id": "550e8400-e29b-41d4-a716-446655440000"
}
```

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/game/merge-guest-data' \
--header 'Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiI3ZjFjNGRlNy1kMTQ0LTQyMzQtOTgwNS1mNGViYWFjMTUzZDEiLCJ0eXAiOiJhY2Nlc3NfdG9rZW4iLCJpZCI6IjI1NDM5Iiwicm9sZSI6MTIsImlzcyI6InVzZXItc2VydmljZSIsImV4cCI6MTc1MzkzNzY3OSwiaWF0IjoxNzUzMzMyODc5fQ.PmPrztd96Q3re6kcWqQFK2GtZlepdqrSqDETzBY-nbk' \
--header 'Content-Type: application/json' \
--data '{
    "guest_session_id": "6d0f3e9c-bc2d-4fe1-8161-48fdf724276b"
}'
```

**Response Success:**
```json
{
    "status": "success",
    "message": "Đã merge dữ liệu guest thành công",
    "data": {
        "guest_session_id": "550e8400-e29b-41d4-a716-446655440000",
        "user_id": 25442,
        "processed_coupons": [
            {
                "prize": {
                    "id": 39,
                    "name": "Voucher 100K",
                    "type": "voucher",
                    "bizStorageId": "12345"
                },
                "coupon": {
                    "couponId": 67890,
                    "couponCode": "VC100K-ABC123",
                    "couponName": "Voucher 100K Tokyo Life",
                    "qrCodeLink": "https://bizfly.vn/qr/VC100K-ABC123",
                    "end_date": "2024-12-31"
                },
                "success": true
            }
        ],
        "errors": []
    }
}
```

**Response Error (Đã merge rồi):**
```json
{
    "status": "error",
    "message": "User đã được merge dữ liệu guest trong game này rồi"
}
```

### 🔍 **Guest Game Flow:**

1. **Guest chưa đăng nhập**: 
   - `GET /api/guest-game/g1/init` → Nhận `guest_session_id`
   - `POST /api/guest-game/g1/claim` → Chơi và nhận prize (lưu tạm thời)

2. **User đăng nhập**: 
   - `POST /api/game/merge-guest-data` → Merge dữ liệu guest + nhận coupon thật từ Bizfly

3. **Lưu ý quan trọng**:
   - Guest chỉ chơi được 1 lần duy nhất
   - Mỗi user chỉ merge được 1 lần trong cùng 1 game
   - Prize của guest được lưu tạm thời, chỉ nhận coupon thật sau khi merge
   - Guest luôn trúng voucher100 ở lần chơi đầu tiên (tương tự authenticated user)

---

### 🌐 **Initialize Guest Game G2**

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/guest-game/g2/init'
```

**Response Success:**
```json
{
    "status": "success",
    "guest_session_id": "550e8400-e29b-41d4-a716-446655440001",
    "play_turns": 1
}
```

### 🎁 **Claim Guest Boxes G2**

**Request Body:**
```json
{
  "guest_session_id": "550e8400-e29b-41d4-a716-446655440001",
  "box_ids": [1, 6, 9]
}
```

**Example Request:**
```bash
curl --location 'https://dev.tokyolife.kamgift.vn/api-v1/guest-game/g2/claim' \
--header 'Content-Type: application/json' \
--data '{
    "guest_session_id": "7c76d8e1-672c-44aa-bf61-61258ed3d8ac",
    "box_ids": [1,3,6]
}'
```

**Response Success:**
```json
{
    "status": "success",
    "message": "Bạn đã mở hộp quà số 1, 3! Đăng nhập để nhận phần thưởng.",
    "guest_session_id": "7c76d8e1-672c-44aa-bf61-61258ed3d8ac",
    "boxes": [
        {
            "id": 1,
            "reward": {
                "reward_id": "45",
                "name": "Giảm 30% toàn bộ balo thông minh",
                "image": "balo_30"
            }
        },
        {
            "id": 2,
            "reward": {
                "reward_id": "38",
                "name": "Code tặng Các sản phẩm \"Tự hào Việt Nam\" dành riêng cho campaign - với hóa đơn bất kỳ",
                "image": "code_tu_hao_vn"
            }
        },
        {
            "id": 3,
            "reward": {
                "reward_id": "38",
                "name": "Code tặng Các sản phẩm \"Tự hào Việt Nam\" dành riêng cho campaign - với hóa đơn bất kỳ",
                "image": "code_tu_hao_vn"
            }
        },
        {
            "id": 4,
            "reward": {
                "reward_id": "45",
                "name": "Giảm 30% toàn bộ balo thông minh",
                "image": "balo_30"
            }
        },
        {
            "id": 5,
            "reward": {
                "reward_id": "38",
                "name": "Code tặng Các sản phẩm \"Tự hào Việt Nam\" dành riêng cho campaign - với hóa đơn bất kỳ",
                "image": "code_tu_hao_vn"
            }
        },
        {
            "id": 6,
            "reward": {
                "reward_id": 0,
                "name": "Chúc bạn may mắn lần sau",
                "image": ""
            }
        },
        {
            "id": 7,
            "reward": {
                "reward_id": "45",
                "name": "Giảm 30% toàn bộ balo thông minh",
                "image": "balo_30"
            }
        },
        {
            "id": 8,
            "reward": {
                "reward_id": "38",
                "name": "Code tặng Các sản phẩm \"Tự hào Việt Nam\" dành riêng cho campaign - với hóa đơn bất kỳ",
                "image": "code_tu_hao_vn"
            }
        },
        {
            "id": 9,
            "reward": {
                "reward_id": "45",
                "name": "Giảm 30% toàn bộ balo thông minh",
                "image": "balo_30"
            }
        }
    ],
    "play_turns": 0
}
```

---